# 分析并删除1.8.8版本中未使用的贴图文件
# PowerShell脚本

Write-Host "分析1.8.8版本中未使用的贴图文件..." -ForegroundColor Green

$textureDir = "src\main\resources\textures"

if (-not (Test-Path $textureDir)) {
    Write-Host "贴图目录不存在: $textureDir" -ForegroundColor Red
    exit 1
}

# 1.8.8版本不支持的物品列表（这些文件可以删除）
$unsupportedItems = @(
    # 1.9+引入的物品
    "shield", "elytra", "end_crystal", "totem_of_undying", "spectral_arrow", "tipped_arrow_base", "tipped_arrow_head",
    "lingering_potion", "splash_potion", "dragon_breath", "chorus_fruit", "popped_chorus_fruit",
    "beetroot", "beetroot_seeds", "beetroot_soup",
    
    # 1.10+
    "structure_void", "magma", "nether_wart_block", "red_nether_brick", "bone_block",
    
    # 1.11+
    "observer", "shulker_shell", "totem", "iron_nugget",
    
    # 1.12+
    "knowledge_book", "concrete", "concrete_powder", "glazed_terracotta",
    
    # 1.13+
    "turtle_helmet", "heart_of_the_sea", "nautilus_shell", "phantom_membrane",
    "trident", "conduit", "kelp", "dried_kelp", "seagrass", "sea_pickle",
    "cod", "salmon", "tropical_fish", "pufferfish", "cod_bucket", "salmon_bucket",
    "tropical_fish_bucket", "pufferfish_bucket", "scute",
    
    # 1.14+
    "crossbow_standby", "crossbow_arrow", "crossbow_firework", "crossbow_pulling_0", "crossbow_pulling_1", "crossbow_pulling_2",
    "suspicious_stew", "sweet_berries", "honeycomb", "honey_bottle",
    "campfire", "lantern", "bell", "bamboo", "scaffolding",
    
    # 1.15+
    "honey_block", "honeycomb_block",
    
    # 1.16+
    "netherite_helmet", "netherite_chestplate", "netherite_leggings", "netherite_boots",
    "netherite_sword", "netherite_pickaxe", "netherite_axe", "netherite_shovel", "netherite_hoe",
    "netherite_ingot", "netherite_scrap", "ancient_debris", "crying_obsidian",
    "soul_campfire", "soul_lantern", "chain", "warped_fungus_on_a_stick",
    "nether_sprouts", "crimson_door", "warped_door", "crimson_sign", "warped_sign",
    
    # 各种新版本的门、告示牌、船等
    "acacia_boat", "acacia_door", "acacia_sign", "birch_boat", "birch_door", "birch_sign",
    "dark_oak_boat", "dark_oak_door", "dark_oak_sign", "jungle_boat", "jungle_door", "jungle_sign",
    "spruce_boat", "spruce_door", "spruce_sign", "oak_boat", "oak_door", "oak_sign",
    
    # 各种染料（1.14+重命名）
    "black_dye", "blue_dye", "brown_dye", "cyan_dye", "gray_dye", "green_dye",
    "light_blue_dye", "light_gray_dye", "lime_dye", "magenta_dye", "orange_dye",
    "pink_dye", "purple_dye", "red_dye", "white_dye", "yellow_dye",
    
    # 各种旗帜图案
    "creeper_banner_pattern", "flower_banner_pattern", "globe_banner_pattern",
    "mojang_banner_pattern", "piglin_banner_pattern", "skull_banner_pattern",
    
    # 其他新版本物品
    "armor_stand", "barrier", "command_block_minecart", "comparator", "repeater",
    "hopper", "cauldron", "brewing_stand", "empty_armor_slot_boots",
    "empty_armor_slot_chestplate", "empty_armor_slot_helmet", "empty_armor_slot_leggings",
    "empty_armor_slot_shield", "filled_map_markings",
    "firework_rocket", "firework_star", "firework_star_overlay", "fishing_rod_cast",
    "bow_pulling_0", "bow_pulling_1", "bow_pulling_2", "potion_overlay", "spawn_egg", "spawn_egg_overlay",
    "leather_boots_overlay", "leather_chestplate_overlay", "leather_helmet_overlay",
    "leather_leggings_overlay", "leather_horse_armor", "iron_horse_armor", "golden_horse_armor",
    "diamond_horse_armor", "lead", "mutton", "cooked_mutton", "rabbit", "cooked_rabbit",
    "rabbit_foot", "rabbit_hide", "rabbit_stew", "poisonous_potato", "broken_elytra"
)

# 获取所有图片文件
$imageFiles = Get-ChildItem -Path $textureDir -Filter "*.png"

Write-Host "找到 $($imageFiles.Count) 个PNG文件" -ForegroundColor Yellow

$filesToDelete = @()
$totalSize = 0

foreach ($file in $imageFiles) {
    $baseName = [System.IO.Path]::GetFileNameWithoutExtension($file.Name)
    
    # 跳过特殊文件
    if ($baseName -eq "README" -or $baseName -eq "enchanted_item_glint") {
        continue
    }
    
    # 检查是否是不支持的物品
    if ($unsupportedItems -contains $baseName) {
        $filesToDelete += $file
        $totalSize += $file.Length
        Write-Host "✗ $($file.Name) - 1.8.8不支持 ($($file.Length) bytes)" -ForegroundColor Red
    } else {
        Write-Host "✓ $($file.Name) - 1.8.8支持" -ForegroundColor Green
    }
}

Write-Host "`n统计结果:" -ForegroundColor Yellow
Write-Host "支持的文件: $($imageFiles.Count - $filesToDelete.Count)" -ForegroundColor Green
Write-Host "不支持的文件: $($filesToDelete.Count)" -ForegroundColor Red
Write-Host "可节省空间: $totalSize bytes ($([math]::Round($totalSize/1024, 1)) KB)" -ForegroundColor Yellow

if ($filesToDelete.Count -gt 0) {
    Write-Host "`n是否删除这些未使用的文件? (y/N): " -NoNewline -ForegroundColor Cyan
    $response = Read-Host
    
    if ($response -match "^[yY]") {
        $deletedCount = 0
        $deletedSize = 0
        
        foreach ($file in $filesToDelete) {
            try {
                $size = $file.Length
                Remove-Item $file.FullName -Force
                $deletedCount++
                $deletedSize += $size
                Write-Host "删除: $($file.Name) ($size bytes)" -ForegroundColor Yellow
            } catch {
                Write-Host "删除失败: $($file.Name) - $($_.Exception.Message)" -ForegroundColor Red
            }
        }
        
        Write-Host "`n删除完成!" -ForegroundColor Green
        Write-Host "删除了 $deletedCount 个文件" -ForegroundColor Green
        Write-Host "节省空间: $deletedSize bytes ($([math]::Round($deletedSize/1024, 1)) KB)" -ForegroundColor Green
    } else {
        Write-Host "取消删除操作" -ForegroundColor Yellow
    }
} else {
    Write-Host "`n没有找到需要删除的文件" -ForegroundColor Green
}

Write-Host "`n分析完成!" -ForegroundColor Green
