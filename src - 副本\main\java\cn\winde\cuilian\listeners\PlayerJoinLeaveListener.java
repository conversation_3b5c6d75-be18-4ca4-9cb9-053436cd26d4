package cn.winde.cuilian.listeners;

import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import cn.winde.cuilian.clbh.Mygui;

/**
 * 玩家加入/退出监听器
 * 用于实时更新UI中的在线玩家列表
 */
public class PlayerJoinLeaveListener implements Listener {

    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        String playerName = event.getPlayer().getName();
        
        // 通知UI更新在线玩家列表
        Mygui.onPlayerJoin(playerName);
    }

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        String playerName = event.getPlayer().getName();
        
        // 通知UI更新在线玩家列表
        Mygui.onPlayerLeave(playerName);
    }
}
