package cn.winde.cuilian.lizi;

import cn.winde.cuilian.Cuilian;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import java.lang.reflect.Method;
import java.lang.reflect.Constructor;

/**
 * PlayerParticles插件集成类
 * 用于集成PlayerParticles插件的特效样式到淬炼系统中
 */
public class PlayerParticlesIntegration {

    private static boolean playerParticlesEnabled = false;
    private static Plugin playerParticlesPlugin = null;

    /**
     * 初始化PlayerParticles集成
     */
    public static void initialize() {
        playerParticlesPlugin = Bukkit.getPluginManager().getPlugin("PlayerParticles");
        if (playerParticlesPlugin != null && playerParticlesPlugin.isEnabled()) {
            // 尝试检测PlayerParticles API类
            try {
                // 检测常见的PlayerParticles API类
                Class.forName("com.esophose.playerparticles.api.PlayerParticlesAPI");
                playerParticlesEnabled = true;
                Cuilian.getInstance().getLogger().info("§a检测到PlayerParticles插件和API，已启用特效集成！");
            } catch (ClassNotFoundException e) {
                // 尝试检测其他可能的API类
                try {
                    Class.forName("com.esophose.playerparticles.styles.api.ParticleStyle");
                    playerParticlesEnabled = true;
                    Cuilian.getInstance().getLogger().info("§a检测到PlayerParticles插件样式API，已启用特效集成！");
                } catch (ClassNotFoundException e2) {
                    playerParticlesEnabled = false;
                    Cuilian.getInstance().getLogger().info("§e检测到PlayerParticles插件但API不可用，使用内置特效系统。");
                }
            }
        } else {
            playerParticlesEnabled = false;
            Cuilian.getInstance().getLogger().info("§e未检测到PlayerParticles插件，使用内置特效系统。");
        }
    }

    /**
     * 检查PlayerParticles是否可用
     */
    public static boolean isPlayerParticlesEnabled() {
        return playerParticlesEnabled && playerParticlesPlugin != null && playerParticlesPlugin.isEnabled();
    }

    /**
     * 使用PlayerParticles样式显示特效
     *
     * @param player    玩家
     * @param styleName 样式名称
     * @param colors    颜色数组
     */
    public static void displayPlayerParticlesEffect(Player player, String styleName,
            ParticleEffect.OrdinaryColor[] colors) {
        if (!isPlayerParticlesEnabled()) {
            // 如果PlayerParticles不可用，回退到内置特效
            displayFallbackEffect(player, styleName, colors);
            return;
        }

        try {
            // 这里可以添加PlayerParticles API调用
            // 由于PlayerParticles的API比较复杂，我们先实现基础的集成
            displayCustomPlayerParticlesEffect(player, styleName, colors);
        } catch (Exception e) {
            // 如果出现异常，回退到内置特效
            Cuilian.getInstance().getLogger().warning("PlayerParticles特效显示失败，回退到内置特效: " + e.getMessage());
            displayFallbackEffect(player, styleName, colors);
        }
    }

    /**
     * 自定义PlayerParticles特效显示
     */
    private static void displayCustomPlayerParticlesEffect(Player player, String styleName,
            ParticleEffect.OrdinaryColor[] colors) {

        switch (styleName.toLowerCase()) {
            // 基础特效
            case "pp_wings":
                displayPlayerParticlesWings(player, colors);
                break;
            case "pp_halo":
                displayPlayerParticlesHalo(player, colors);
                break;
            case "pp_spiral":
                displayPlayerParticlesSpiral(player, colors);
                break;
            case "pp_orbit":
                displayPlayerParticlesOrbit(player, colors);
                break;
            case "pp_sphere":
                displayPlayerParticlesSphere(player, colors);
                break;
            // 新增的PlayerParticles特效
            case "pp_beam":
                displayPlayerParticlesBeam(player, colors);
                break;
            case "pp_cube":
                displayPlayerParticlesCube(player, colors);
                break;
            case "pp_quadhelix":
                displayPlayerParticlesQuadhelix(player, colors);
                break;
            case "pp_spin":
                displayPlayerParticlesSpin(player, colors);
                break;
            case "pp_thick":
                displayPlayerParticlesThick(player, colors);
                break;
            case "pp_feet":
                displayPlayerParticlesFeet(player, colors);
                break;
            case "pp_point":
                displayPlayerParticlesPoint(player, colors);
                break;
            case "pp_arrows":
                displayPlayerParticlesArrows(player, colors);
                break;
            case "pp_swords":
                displayPlayerParticlesSwords(player, colors);
                break;
            case "pp_move":
                displayPlayerParticlesMove(player, colors);
                break;
            case "pp_blockbreak":
                displayPlayerParticlesBlockBreak(player, colors);
                break;
            case "pp_hurt":
                displayPlayerParticlesHurt(player, colors);
                break;
            default:
                // 未知样式，使用默认特效
                displayFallbackEffect(player, styleName, colors);
                break;
        }
    }

    /**
     * PlayerParticles风格的翅膀特效
     */
    private static void displayPlayerParticlesWings(Player player, ParticleEffect.OrdinaryColor[] colors) {
        Location location = player.getLocation().clone().add(0, 1.8, 0);

        // 模拟PlayerParticles的翅膀算法
        for (double t = 0; t < Math.PI * 2; t += Math.PI / 32) {
            double x = Math.sin(t)
                    * (Math.pow(Math.E, Math.cos(t)) - 2 * Math.cos(t * 4) - Math.pow(Math.sin(t / 12), 5)) / 3;
            double y = Math.cos(t)
                    * (Math.pow(Math.E, Math.cos(t)) - 2 * Math.cos(t * 4) - Math.pow(Math.sin(t / 12), 5)) / 3;

            // 旋转以匹配玩家朝向
            double yaw = Math.toRadians(-location.getYaw());
            double rotatedX = x * Math.cos(yaw) - (-0.3) * Math.sin(yaw);
            double rotatedZ = x * Math.sin(yaw) + (-0.3) * Math.cos(yaw);

            Location particleLoc = location.clone().add(rotatedX, y, rotatedZ);

            // 使用颜色
            ParticleEffect.OrdinaryColor color = colors[0];
            if (colors.length > 1 && Math.random() > 0.5) {
                color = colors[1];
            }

            ParticleEffect.REDSTONE.display(color, particleLoc, 30.0);
        }
    }

    /**
     * PlayerParticles风格的光环特效
     */
    private static void displayPlayerParticlesHalo(Player player, ParticleEffect.OrdinaryColor[] colors) {
        Location location = player.getLocation().clone().add(0, 2.2, 0);

        int points = 20;
        double radius = 0.7;
        double slice = 2 * Math.PI / points;

        for (int i = 0; i < points; i++) {
            double angle = slice * i;
            double newX = location.getX() + radius * Math.cos(angle);
            double newZ = location.getZ() + radius * Math.sin(angle);

            Location particleLoc = new Location(location.getWorld(), newX, location.getY(), newZ);

            // 循环使用颜色
            ParticleEffect.OrdinaryColor color = colors[i % colors.length];
            ParticleEffect.REDSTONE.display(color, particleLoc, 30.0);
        }
    }

    /**
     * PlayerParticles风格的螺旋特效
     */
    private static void displayPlayerParticlesSpiral(Player player, ParticleEffect.OrdinaryColor[] colors) {
        Location location = player.getLocation().clone();

        double height = 3.0;
        double radius = 0.8;
        int points = 30;

        for (int i = 0; i < points; i++) {
            double ratio = (double) i / points;
            double y = ratio * height;
            double angle = ratio * 6 * Math.PI; // 三圈螺旋

            double x = radius * Math.cos(angle) * (1 - ratio * 0.3); // 向上收缩
            double z = radius * Math.sin(angle) * (1 - ratio * 0.3);

            Location particleLoc = location.clone().add(x, y, z);

            // 根据高度选择颜色
            int colorIndex = (int) (ratio * colors.length);
            if (colorIndex >= colors.length)
                colorIndex = colors.length - 1;

            ParticleEffect.REDSTONE.display(colors[colorIndex], particleLoc, 30.0);
        }
    }

    /**
     * PlayerParticles风格的轨道特效
     */
    private static void displayPlayerParticlesOrbit(Player player, ParticleEffect.OrdinaryColor[] colors) {
        Location location = player.getLocation().clone().add(0, 1.0, 0);

        // 创建多个轨道
        for (int orbit = 0; orbit < Math.min(3, colors.length); orbit++) {
            double radius = 0.6 + orbit * 0.4;
            double height = orbit * 0.3;
            int particles = 12;

            for (int i = 0; i < particles; i++) {
                double angle = 2 * Math.PI * i / particles;
                angle += orbit * Math.PI / 4; // 轨道间偏移

                double x = radius * Math.cos(angle);
                double z = radius * Math.sin(angle);

                Location orbitLoc = location.clone().add(x, height, z);
                ParticleEffect.REDSTONE.display(colors[orbit], orbitLoc, 30.0);
            }
        }
    }

    /**
     * PlayerParticles风格的球体特效
     */
    private static void displayPlayerParticlesSphere(Player player, ParticleEffect.OrdinaryColor[] colors) {
        Location location = player.getLocation().clone().add(0, 1.5, 0);

        double radius = 1.0;
        int points = 40;

        for (int i = 0; i < points; i++) {
            // 使用斐波那契球面分布
            double y = 1 - (i / (double) (points - 1)) * 2; // y从1到-1
            double radiusAtY = Math.sqrt(1 - y * y);

            double theta = Math.PI * (3 - Math.sqrt(5)) * i; // 黄金角

            double x = Math.cos(theta) * radiusAtY;
            double z = Math.sin(theta) * radiusAtY;

            Location particleLoc = location.clone().add(x * radius, y * radius, z * radius);

            // 根据y坐标选择颜色
            int colorIndex = (int) ((y + 1) / 2 * colors.length);
            if (colorIndex >= colors.length)
                colorIndex = colors.length - 1;

            ParticleEffect.REDSTONE.display(colors[colorIndex], particleLoc, 30.0);
        }
    }

    /**
     * 回退特效（当PlayerParticles不可用时）
     */
    private static void displayFallbackEffect(Player player, String styleName, ParticleEffect.OrdinaryColor[] colors) {
        // 使用内置特效系统
        ParticleEffect.OrdinaryColor c1 = colors.length > 0 ? colors[0] : new ParticleEffect.OrdinaryColor(255, 0, 0);
        ParticleEffect.OrdinaryColor c2 = colors.length > 1 ? colors[1] : new ParticleEffect.OrdinaryColor(0, 255, 0);
        ParticleEffect.OrdinaryColor c3 = colors.length > 2 ? colors[2] : new ParticleEffect.OrdinaryColor(0, 0, 255);

        // 根据样式名称选择对应的内置特效
        String effectType = styleName.replace("pp_", ""); // 移除pp_前缀

        switch (effectType.toLowerCase()) {
            case "wings":
                effectlisten.SpawnWings(player, c1, c2, c3);
                break;
            case "halo":
                effectlisten.SpawnHalo(player, c1, c2, c3);
                break;
            case "spiral":
                effectlisten.SpawnSpiral(player, c1, c2, c3);
                break;
            case "orbit":
                effectlisten.SpawnOrbit(player, c1, c2, c3);
                break;
            case "sphere":
                effectlisten.SpawnSphere(player, c1, c2, c3);
                break;
            default:
                effectlisten.SpawnWings(player, c1, c2, c3); // 默认翅膀
                break;
        }
    }

    /**
     * PlayerParticles风格的光束特效
     */
    private static void displayPlayerParticlesBeam(Player player, ParticleEffect.OrdinaryColor[] colors) {
        Location location = player.getLocation().clone();

        // 模拟Beam特效 - 上下移动的圆形光束
        int points = 16;
        double radius = 1.0;
        double slice = 2 * Math.PI / points;

        // 使用时间来计算高度偏移
        long time = System.currentTimeMillis() / 100;
        double heightOffset = Math.sin(time * 0.1) * 1.5;

        for (int i = 0; i < points; i++) {
            double angle = slice * i;
            double x = radius * Math.cos(angle);
            double z = radius * Math.sin(angle);
            double y = heightOffset;

            Location particleLoc = location.clone().add(x, y, z);

            // 循环使用颜色
            ParticleEffect.OrdinaryColor color = colors[i % colors.length];
            ParticleEffect.REDSTONE.display(color, particleLoc, 30.0);
        }
    }

    /**
     * PlayerParticles风格的立方体特效
     */
    private static void displayPlayerParticlesCube(Player player, ParticleEffect.OrdinaryColor[] colors) {
        Location location = player.getLocation().clone().add(0, 1.0, 0);

        // 旋转的立方体
        long time = System.currentTimeMillis() / 50;
        double rotation = time * 0.05;

        double size = 1.5;
        int particlesPerEdge = 5;

        // 绘制立方体的边
        for (int edge = 0; edge < 12; edge++) {
            for (int i = 0; i < particlesPerEdge; i++) {
                double ratio = (double) i / (particlesPerEdge - 1);
                double[] coords = getCubeEdgeCoords(size, edge, ratio);

                // 应用旋转
                double x = coords[0] * Math.cos(rotation) - coords[2] * Math.sin(rotation);
                double y = coords[1];
                double z = coords[0] * Math.sin(rotation) + coords[2] * Math.cos(rotation);

                Location particleLoc = location.clone().add(x, y, z);

                // 根据边的索引使用不同颜色
                ParticleEffect.OrdinaryColor color = colors[edge % colors.length];
                ParticleEffect.REDSTONE.display(color, particleLoc, 30.0);
            }
        }
    }

    /**
     * 获取立方体边的坐标
     */
    private static double[] getCubeEdgeCoords(double size, int edge, double ratio) {
        double half = size / 2;
        double x = 0, y = 0, z = 0;

        switch (edge) {
            // 底面的4条边
            case 0:
                x = -half + ratio * size;
                y = -half;
                z = -half;
                break;
            case 1:
                x = half;
                y = -half;
                z = -half + ratio * size;
                break;
            case 2:
                x = half - ratio * size;
                y = -half;
                z = half;
                break;
            case 3:
                x = -half;
                y = -half;
                z = half - ratio * size;
                break;
            // 顶面的4条边
            case 4:
                x = -half + ratio * size;
                y = half;
                z = -half;
                break;
            case 5:
                x = half;
                y = half;
                z = -half + ratio * size;
                break;
            case 6:
                x = half - ratio * size;
                y = half;
                z = half;
                break;
            case 7:
                x = -half;
                y = half;
                z = half - ratio * size;
                break;
            // 垂直的4条边
            case 8:
                x = -half;
                y = -half + ratio * size;
                z = -half;
                break;
            case 9:
                x = half;
                y = -half + ratio * size;
                z = -half;
                break;
            case 10:
                x = half;
                y = -half + ratio * size;
                z = half;
                break;
            case 11:
                x = -half;
                y = -half + ratio * size;
                z = half;
                break;
        }

        return new double[] { x, y, z };
    }

    /**
     * PlayerParticles风格的四重螺旋特效
     */
    private static void displayPlayerParticlesQuadhelix(Player player, ParticleEffect.OrdinaryColor[] colors) {
        Location location = player.getLocation().clone();

        // 四重螺旋，上下移动
        long time = System.currentTimeMillis() / 100;
        double heightOffset = Math.sin(time * 0.1) * 2.0;

        for (int i = 0; i < 4; i++) {
            double angle = (time * 0.1) + (Math.PI / 2 * i);
            double radius = (60 - Math.abs(heightOffset * 30)) / 60;

            double x = Math.cos(angle) * radius;
            double z = Math.sin(angle) * radius;
            double y = heightOffset;

            Location particleLoc = location.clone().add(x, y + 1.5, z);

            // 每个螺旋使用不同颜色
            ParticleEffect.OrdinaryColor color = colors[i % colors.length];
            ParticleEffect.REDSTONE.display(color, particleLoc, 30.0);
        }
    }

    /**
     * PlayerParticles风格的旋转特效
     */
    private static void displayPlayerParticlesSpin(Player player, ParticleEffect.OrdinaryColor[] colors) {
        Location location = player.getLocation().clone().add(0, 1.5, 0);

        // 单点旋转
        long time = System.currentTimeMillis() / 100;
        double angle = time * 0.2;
        double radius = 0.5;

        double x = radius * Math.cos(angle);
        double z = radius * Math.sin(angle);

        Location particleLoc = location.clone().add(x, 0, z);

        // 使用第一个颜色
        ParticleEffect.OrdinaryColor color = colors[0];
        ParticleEffect.REDSTONE.display(color, particleLoc, 30.0);

        // 添加一些额外的粒子形成轨迹
        for (int i = 1; i <= 3; i++) {
            double trailAngle = angle - (i * 0.3);
            double trailX = radius * Math.cos(trailAngle) * (1 - i * 0.2);
            double trailZ = radius * Math.sin(trailAngle) * (1 - i * 0.2);

            Location trailLoc = location.clone().add(trailX, 0, trailZ);
            ParticleEffect.REDSTONE.display(color, trailLoc, 30.0);
        }
    }

    /**
     * PlayerParticles风格的浓密特效
     */
    private static void displayPlayerParticlesThick(Player player, ParticleEffect.OrdinaryColor[] colors) {
        Location location = player.getLocation().clone().add(0, 1.0, 0);

        // 在玩家周围生成大量粒子
        for (int i = 0; i < 15; i++) {
            double x = (Math.random() - 0.5) * 2;
            double y = (Math.random() - 0.5) * 2;
            double z = (Math.random() - 0.5) * 2;

            Location particleLoc = location.clone().add(x, y, z);

            // 随机使用颜色
            ParticleEffect.OrdinaryColor color = colors[(int) (Math.random() * colors.length)];
            ParticleEffect.REDSTONE.display(color, particleLoc, 30.0);
        }
    }

    /**
     * PlayerParticles风格的脚部特效
     */
    private static void displayPlayerParticlesFeet(Player player, ParticleEffect.OrdinaryColor[] colors) {
        Location location = player.getLocation().clone().subtract(0, 0.95, 0);

        // 在脚部生成粒子
        for (int i = 0; i < 5; i++) {
            double x = (Math.random() - 0.5) * 0.8;
            double z = (Math.random() - 0.5) * 0.8;

            Location particleLoc = location.clone().add(x, 0, z);

            // 使用第一个颜色
            ParticleEffect.OrdinaryColor color = colors[0];
            ParticleEffect.REDSTONE.display(color, particleLoc, 30.0);
        }
    }

    /**
     * PlayerParticles风格的点特效
     */
    private static void displayPlayerParticlesPoint(Player player, ParticleEffect.OrdinaryColor[] colors) {
        Location location = player.getLocation().clone().add(0, 1.5, 0);

        // 单点粒子
        ParticleEffect.OrdinaryColor color = colors[0];
        ParticleEffect.REDSTONE.display(color, location, 30.0);

        // 添加一些闪烁效果
        if (Math.random() > 0.7) {
            ParticleEffect.FIREWORKS_SPARK.display(0, 0, 0, 0, 1, location, 30.0);
        }
    }

    /**
     * PlayerParticles风格的箭矢特效
     */
    private static void displayPlayerParticlesArrows(Player player, ParticleEffect.OrdinaryColor[] colors) {
        Location location = player.getLocation().clone().add(0, 1.0, 0);

        // 模拟箭矢轨迹
        for (int i = 0; i < 3; i++) {
            double angle = Math.random() * Math.PI * 2;
            double distance = 1.0 + Math.random() * 2.0;

            double x = Math.cos(angle) * distance;
            double z = Math.sin(angle) * distance;
            double y = Math.random() * 1.5;

            Location particleLoc = location.clone().add(x, y, z);

            // 使用不同颜色
            ParticleEffect.OrdinaryColor color = colors[i % colors.length];
            ParticleEffect.REDSTONE.display(color, particleLoc, 30.0);
        }
    }

    /**
     * PlayerParticles风格的剑击特效
     */
    private static void displayPlayerParticlesSwords(Player player, ParticleEffect.OrdinaryColor[] colors) {
        Location location = player.getLocation().clone().add(0, 1.0, 0);

        // 剑击效果 - 扇形粒子
        double playerYaw = Math.toRadians(player.getLocation().getYaw());

        for (int i = 0; i < 8; i++) {
            double angle = playerYaw + (Math.random() - 0.5) * Math.PI / 2;
            double distance = 1.0 + Math.random() * 1.5;

            double x = Math.cos(angle) * distance;
            double z = Math.sin(angle) * distance;
            double y = (Math.random() - 0.5) * 1.0;

            Location particleLoc = location.clone().add(x, y, z);

            // 使用随机颜色
            ParticleEffect.OrdinaryColor color = colors[(int) (Math.random() * colors.length)];
            ParticleEffect.REDSTONE.display(color, particleLoc, 30.0);
        }
    }

    /**
     * PlayerParticles风格的移动特效
     */
    private static void displayPlayerParticlesMove(Player player, ParticleEffect.OrdinaryColor[] colors) {
        Location location = player.getLocation().clone().add(0, 0.05, 0);

        // 移动轨迹特效
        for (int i = 0; i < 3; i++) {
            double x = (Math.random() - 0.5) * 0.5;
            double z = (Math.random() - 0.5) * 0.5;

            Location particleLoc = location.clone().add(x, 0, z);

            // 使用第一个颜色
            ParticleEffect.OrdinaryColor color = colors[0];
            ParticleEffect.REDSTONE.display(color, particleLoc, 30.0);
        }
    }

    /**
     * PlayerParticles风格的方块破坏特效
     */
    private static void displayPlayerParticlesBlockBreak(Player player, ParticleEffect.OrdinaryColor[] colors) {
        Location location = player.getLocation().clone().add(0, 1.0, 0);

        // 方块破坏效果 - 爆炸式粒子
        for (int i = 0; i < 15; i++) {
            double x = (Math.random() - 0.5) * 1.0;
            double y = (Math.random() - 0.5) * 1.0;
            double z = (Math.random() - 0.5) * 1.0;

            Location particleLoc = location.clone().add(x, y, z);

            // 使用随机颜色
            ParticleEffect.OrdinaryColor color = colors[(int) (Math.random() * colors.length)];
            ParticleEffect.REDSTONE.display(color, particleLoc, 30.0);
        }

        // 添加一些爆炸粒子
        ParticleEffect.EXPLOSION_NORMAL.display(0, 0, 0, 0, 3, location, 30.0);
    }

    /**
     * PlayerParticles风格的受伤特效
     */
    private static void displayPlayerParticlesHurt(Player player, ParticleEffect.OrdinaryColor[] colors) {
        Location location = player.getLocation().clone().add(0, 1.0, 0);

        // 受伤效果 - 红色粒子爆发
        for (int i = 0; i < 20; i++) {
            double x = (Math.random() - 0.5) * 1.5;
            double y = (Math.random() - 0.5) * 1.5;
            double z = (Math.random() - 0.5) * 1.5;

            Location particleLoc = location.clone().add(x, y, z);

            // 主要使用红色，混合其他颜色
            ParticleEffect.OrdinaryColor color;
            if (Math.random() > 0.3) {
                color = new ParticleEffect.OrdinaryColor(255, 0, 0); // 红色
            } else {
                color = colors[(int) (Math.random() * colors.length)];
            }

            ParticleEffect.REDSTONE.display(color, particleLoc, 30.0);
        }

        // 添加一些血液效果
        ParticleEffect.CRIT.display(0, 0, 0, 0, 5, location, 30.0);
    }

    /**
     * 获取所有可用的特效样式名称
     */
    public static String[] getAvailableStyles() {
        if (isPlayerParticlesEnabled()) {
            return new String[] {
                    "wings", "halo", "flame", "nebula", "tornado", "stars",
                    "spiral", "sphere", "orbit", "cube", "helix",
                    "pp_wings", "pp_halo", "pp_spiral", "pp_orbit", "pp_sphere",
                    "pp_beam", "pp_cube", "pp_quadhelix", "pp_spin", "pp_thick",
                    "pp_feet", "pp_point", "pp_arrows", "pp_swords", "pp_move",
                    "pp_blockbreak", "pp_hurt"
            };
        } else {
            return new String[] {
                    "wings", "halo", "flame", "nebula", "tornado", "stars",
                    "spiral", "sphere", "orbit", "cube", "helix"
            };
        }
    }
}
