package cn.winde.cuilian.util;

import cn.winde.cuilian.Cuilian;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.Map;

/**
 * 套装特效消息管理器
 * 用于控制套装特效激活/失去消息的显示
 */
public class SuitMessageManager {
    
    // 存储玩家的消息开关状态 (玩家名 -> 是否启用消息)
    private static final Map<String, Boolean> playerMessageEnabled = new HashMap<>();
    
    /**
     * 检查是否应该发送套装特效消息
     * 
     * @param playerName 玩家名
     * @return 是否应该发送消息
     */
    public static boolean shouldSendMessage(String playerName) {
        // 检查配置文件中的强制关闭设置
        boolean forceDisabled = Cuilian.config.getBoolean("suit_effect_messages.force_disabled", false);
        if (forceDisabled) {
            return false;
        }
        
        // 检查全局消息开关
        boolean globalEnabled = Cuilian.config.getBoolean("suit_effect_messages.enabled", true);
        if (!globalEnabled) {
            return false;
        }
        
        // 检查玩家个人消息设置
        return isPlayerMessageEnabled(playerName);
    }
    
    /**
     * 检查玩家是否启用套装特效消息
     * 
     * @param playerName 玩家名
     * @return 是否启用消息 (默认为true)
     */
    public static boolean isPlayerMessageEnabled(String playerName) {
        return playerMessageEnabled.getOrDefault(playerName, true);
    }
    
    /**
     * 设置玩家套装特效消息状态
     * 
     * @param playerName 玩家名
     * @param enabled 是否启用消息
     */
    public static void setPlayerMessageEnabled(String playerName, boolean enabled) {
        playerMessageEnabled.put(playerName, enabled);
    }
    
    /**
     * 检查配置文件中是否强制禁用消息
     * 
     * @return 是否强制禁用
     */
    public static boolean isForceDisabled() {
        return Cuilian.config.getBoolean("suit_effect_messages.force_disabled", false);
    }
    
    /**
     * 检查全局消息开关是否启用
     * 
     * @return 全局消息是否启用
     */
    public static boolean isGlobalMessageEnabled() {
        return Cuilian.config.getBoolean("suit_effect_messages.enabled", true);
    }
    
    /**
     * 发送套装特效激活消息（如果允许）
     * 
     * @param player 玩家
     * @param level 套装等级
     */
    public static void sendActivationMessage(Player player, int level) {
        if (shouldSendMessage(player.getName())) {
            player.sendMessage("§a你激活了" + Cuilian.getCuilianStr(level) + "§a套装特效...");
        }
    }
    
    /**
     * 发送套装特效失去消息（如果允许）
     * 
     * @param player 玩家
     */
    public static void sendDeactivationMessage(Player player) {
        if (shouldSendMessage(player.getName())) {
            player.sendMessage("§c你失去了套装特效...");
        }
    }
    
    /**
     * 清除玩家的消息设置（玩家离线时调用）
     * 
     * @param playerName 玩家名
     */
    public static void clearPlayerSettings(String playerName) {
        playerMessageEnabled.remove(playerName);
    }
    
    /**
     * 获取所有玩家的消息设置（用于调试）
     * 
     * @return 玩家消息设置映射
     */
    public static Map<String, Boolean> getAllPlayerSettings() {
        return new HashMap<>(playerMessageEnabled);
    }
}
