/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.configuration.file.FileConfiguration
 */
package cn.winde.cuilian;

import cn.winde.cuilian.Cuilian;
import org.bukkit.configuration.file.FileConfiguration;

public class Loadlang {
    public static void loadProbability(FileConfiguration cfg) {
        Cuilian.putonggl.clear();
        for (String s : cfg.getConfigurationSection("putong").getKeys(false)) {
            Cuilian.putonggl.put(Integer.valueOf(s), cfg.getInt("putong." + s));
        }
        Cuilian.zhongdenggl.clear();
        for (String s : cfg.getConfigurationSection("zhongdeng").getKeys(false)) {
            Cuilian.zhongdenggl.put(Integer.valueOf(s), cfg.getInt("zhongdeng." + s));
        }
        Cuilian.gaodenggl.clear();
        for (String s : cfg.getConfigurationSection("gaodeng").getKeys(false)) {
            Cuilian.gaodenggl.put(Integer.valueOf(s), cfg.getInt("gaodeng." + s));
        }
        Cuilian.wanmeigl.clear();
        for (String s : cfg.getConfigurationSection("wanmei").getKeys(false)) {
            Cuilian.wanmeigl.put(Integer.valueOf(s), cfg.getInt("wanmei." + s));
        }
    }

    public static void loadWeapon(FileConfiguration cfg) {
        Cuilian.wuqishanghai.clear();
        for (String s : cfg.getConfigurationSection("shanghai").getKeys(false)) {
            Cuilian.wuqishanghai.put(Integer.valueOf(s), cfg.getInt("shanghai." + s));
        }
        Cuilian.xixue.clear();
        for (String s : cfg.getConfigurationSection("xixue").getKeys(false)) {
            Cuilian.xixue.put(Integer.valueOf(s), cfg.getInt("xixue." + s));
        }
        Cuilian.jump.clear();
        for (String s : cfg.getConfigurationSection("jump").getKeys(false)) {
            Cuilian.jump.put(Integer.valueOf(s), cfg.getInt("jump." + s));
        }
        Cuilian.js.clear();
        for (String s : cfg.getConfigurationSection("js").getKeys(false)) {
            Cuilian.js.put(Integer.valueOf(s), cfg.getInt("js." + s));
        }
        Cuilian.hujiafangyu.clear();
        for (String s : cfg.getConfigurationSection("Defense").getKeys(false)) {
            Cuilian.hujiafangyu.put(Integer.valueOf(s), cfg.getInt("Defense." + s));
        }
        Cuilian.fx.clear();
        for (String s : cfg.getConfigurationSection("fs").getKeys(false)) {
            Cuilian.fx.put(Integer.valueOf(s), cfg.getInt("fs." + s));
        }
    }
}

