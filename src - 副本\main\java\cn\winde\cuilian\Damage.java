/*
 * Decompiled with CFR 0.152.
 *
 * Could not load the following classes:
 *  org.bukkit.Material
 *  org.bukkit.entity.EntityType
 *  org.bukkit.entity.Player
 *  org.bukkit.entity.Projectile
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.entity.EntityDamageByEntityEvent
 *  org.bukkit.event.entity.EntityDamageEvent
 *  org.bukkit.event.entity.EntityDamageEvent$DamageCause
 *  org.bukkit.event.entity.ProjectileLaunchEvent
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 */
package cn.winde.cuilian;

import cn.winde.cuilian.Cuilian;
import cn.winde.cuilian.suit.SuitManager;
import java.util.HashMap;
import java.util.List;
import org.bukkit.Material;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.entity.Projectile;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.ProjectileLaunchEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

public class Damage
implements Listener {
    public static HashMap<Integer, Integer> damage = new HashMap();

    @EventHandler
    public void onProjectileFire(ProjectileLaunchEvent e) {
        ItemStack in_hand;
        int Leve;
        Player p;
        if (e.getEntity().getShooter() instanceof Player && (p = (Player)e.getEntity().getShooter()).getItemInHand() != null && p.getItemInHand().getType() != Material.AIR && (Leve = Cuilian.getCuilianlevel(in_hand = p.getItemInHand())) > 0) {
            if (damage == null) {
                damage = new HashMap();
            }
            damage.put(e.getEntity().getEntityId(), Leve);
        }
    }

    @EventHandler
    public void onEntityDamage(EntityDamageByEntityEvent e) {
        Player damager;
        if (e.getDamager() instanceof Projectile && damage.containsKey(e.getDamager().getEntityId())) {
            int ID = e.getDamager().getEntityId();
            int dj = damage.get(ID);
            damage.remove(ID);
            double jieguo = e.getDamage();
            if (e.getEntity() instanceof Player) {
                jieguo = dj > 0 ? (jieguo += (double)Cuilian.wuqishanghai.get(dj).intValue() - this.fangyuzhi((Player)e.getEntity())) : (jieguo -= this.fangyuzhi((Player)e.getEntity()));
            } else if (dj > 0) {
                jieguo += (double)Cuilian.wuqishanghai.get(dj).intValue();
            }
            if (jieguo < 0.0) {
                e.setDamage(0.0);
            } else {
                e.setDamage(jieguo);
            }
        }
        if (e.getDamager() instanceof Player && e.getEntity().getType() != EntityType.PLAYER) {
            ItemStack in_hand;
            Player damager2 = (Player)e.getDamager();
            if (damager2.getItemInHand() != null && damager2.getItemInHand().getType() != Material.AIR && (in_hand = damager2.getItemInHand()).getItemMeta().hasLore()) {
                int dj;
                ItemMeta meta = in_hand.getItemMeta();
                String item = meta.getDisplayName();
                int id = in_hand.getTypeId();
                if ((id == 276 || id == 283 || id == 272 || id == 268 || id == 267) && (dj = Cuilian.getCuilianlevel(in_hand)) > 0) {
                    e.setDamage(e.getDamage() + (double)Cuilian.wuqishanghai.get(dj).intValue());
                    double xixue = Cuilian.xixue.get(dj).doubleValue() / 100.0;
                    double addxue = e.getDamage() * xixue;
                    if (damager2.getHealth() + addxue <= damager2.getMaxHealth()) {
                        damager2.setHealth(damager2.getHealth() + addxue);
                    }
                    if (damager2.getHealth() + addxue > damager2.getMaxHealth()) {
                        damager2.setHealth(damager2.getMaxHealth());
                    }
                    return;
                }
            }
            return;
        }
        if (e.getDamager() instanceof Player && e.getEntity().getType() == EntityType.PLAYER && (damager = (Player)e.getDamager()).getItemInHand() != null && damager.getItemInHand().getType() != Material.AIR) {
            ItemStack in_hand = damager.getItemInHand();
            int dj = Cuilian.getCuilianlevel(in_hand);
            double addxue = 0.0;
            double jieguo = 0.0;

            // 获取套装属性加成
            SuitManager.SuitAttribute suitAttribute = SuitManager.getPlayerSuitAttribute(damager.getName());
            double suitAttackDamage = 0.0;
            double suitVampire = 0.0;

            if (suitAttribute != null) {
                suitAttackDamage = suitAttribute.attackDamage;
                suitVampire = suitAttribute.vampire / 100.0;
            }

            if (dj > 0) {
                jieguo = e.getDamage() + ((double)Cuilian.wuqishanghai.get(dj).intValue() + suitAttackDamage - this.fangyuzhi((Player)e.getEntity()));
                double xixue = Cuilian.xixue.get(dj).doubleValue() / 100.0 + suitVampire;
                addxue = e.getDamage() * xixue;
            } else {
                jieguo = e.getDamage() + suitAttackDamage - this.fangyuzhi((Player)e.getEntity());
                addxue = e.getDamage() * suitVampire;
            }

            if (jieguo < 0.0) {
                e.setDamage(0.0);
            } else {
                e.setDamage(jieguo);
            }

            if (damager.getHealth() > 0.0 && addxue > 0.0) {
                if (damager.getHealth() + addxue <= damager.getMaxHealth()) {
                    damager.setHealth(damager.getHealth() + addxue);
                }
                if (damager.getHealth() + addxue > damager.getMaxHealth()) {
                    damager.setHealth(damager.getMaxHealth());
                }
            }
        }
    }

    public int RpgDamage(List<String> arr) {
        int ser = 0;
        for (String in : arr) {
            if (arr == null || in.indexOf("Damage") <= -1) continue;
            String[] str = in.split(" Damage")[0].split("§f");
            ser = Integer.valueOf(str[1]);
            break;
        }
        return ser;
    }

    @EventHandler
    public void onEntityDamage0(EntityDamageEvent e) {
        if (e.getEntity() instanceof Player) {
            ItemMeta meta;
            ItemStack tui;
            Player p = (Player)e.getEntity();
            if (e.getCause() == EntityDamageEvent.DamageCause.FALL && (tui = p.getInventory().getLeggings()) != null && tui.hasItemMeta() && (meta = tui.getItemMeta()).hasLore()) {
                float bfb = Cuilian.js.get(Cuilian.getCuilianlevel(tui)).floatValue();
                double js = bfb / 100.0f;
                e.setDamage(e.getDamage() - e.getDamage() * js);
            }
        }
    }

    public double fangyuzhi(Player p) {
        ItemMeta meta;
        ItemStack xie;
        ItemMeta meta2;
        ItemStack tui;
        ItemMeta meta3;
        ItemStack xiong;
        ItemMeta meta4;
        double jianmian = 0.0;

        // 计算装备防御力
        ItemStack tou = p.getInventory().getHelmet();
        if (tou != null && tou.hasItemMeta() && (meta4 = tou.getItemMeta()).hasLore()) {
            jianmian += (double)Cuilian.hujiafangyu.get(Cuilian.getCuilianlevel(tou)).intValue();
        }
        if ((xiong = p.getInventory().getChestplate()) != null && xiong.hasItemMeta() && (meta3 = xiong.getItemMeta()).hasLore()) {
            jianmian += (double)Cuilian.hujiafangyu.get(Cuilian.getCuilianlevel(xiong)).intValue();
        }
        if ((tui = p.getInventory().getLeggings()) != null && tui.hasItemMeta() && (meta2 = tui.getItemMeta()).hasLore()) {
            jianmian += (double)Cuilian.hujiafangyu.get(Cuilian.getCuilianlevel(tui)).intValue();
        }
        if ((xie = p.getInventory().getBoots()) != null && xie.hasItemMeta() && (meta = xie.getItemMeta()).hasLore()) {
            jianmian += (double)Cuilian.hujiafangyu.get(Cuilian.getCuilianlevel(xie)).intValue();
        }

        // 添加套装防御力加成
        SuitManager.SuitAttribute suitAttribute = SuitManager.getPlayerSuitAttribute(p.getName());
        if (suitAttribute != null) {
            jianmian += suitAttribute.defense;
        }

        return jianmian;
    }
}

