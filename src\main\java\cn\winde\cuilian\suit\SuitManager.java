package cn.winde.cuilian.suit;

import cn.winde.cuilian.Cuilian;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

import java.util.*;

/**
 * 套装管理器
 * 负责处理套装识别、属性加成和特效管理
 */
public class SuitManager {

    // 存储玩家当前的套装信息
    private static final Map<String, String> playerSuits = new HashMap<>();
    // 存储玩家的套装属性加成
    private static final Map<String, SuitAttribute> playerSuitAttributes = new HashMap<>();
    // 存储玩家特效显示开关状态 (true=开启, false=关闭)
    private static final Map<String, Boolean> playerEffectEnabled = new HashMap<>();
    // 存储玩家套装给予偏好 (true=自动装备, false=放入背包) 默认false
    private static final Map<String, Boolean> playerAutoEquipPreference = new HashMap<>();

    /**
     * 品质配置类
     */
    public static class QualityConfig {
        public String name;
        public String color;
        public String description;

        public QualityConfig(String name, String color, String description) {
            this.name = name;
            this.color = color;
            this.description = description;
        }
    }

    /**
     * 特效配置类
     */
    public static class EffectConfig {
        public String type;
        public boolean enabled;
        public String colore1;
        public String colore2;
        public String colore3;

        public EffectConfig(String type, boolean enabled, String colore1, String colore2, String colore3) {
            this.type = type;
            this.enabled = enabled;
            this.colore1 = colore1;
            this.colore2 = colore2;
            this.colore3 = colore3;
        }
    }

    /**
     * 套装属性类
     */
    public static class SuitAttribute {
        public int attackDamage;
        public int defense;
        public int health;
        public double speed;
        public int jump;
        public int vampire;
        // 无限耐久
        public boolean infiniteDurability;
        // 兼容旧格式的单特效
        public String effectType;
        public String color1;
        public String color2;
        public String color3;
        // 新的多特效支持
        public boolean enableStacking;
        public List<EffectConfig> effects;
        public List<String> description;
        // 装备名称
        public String helmet;
        public String chestplate;
        public String leggings;
        public String boots;
        // 武器名称
        public String sword;
        public String bow;
        // 品质等级
        public int quality;

        public SuitAttribute(int attackDamage, int defense, int health, double speed,
                int jump, int vampire, boolean infiniteDurability, String effectType, String color1,
                String color2, String color3, boolean enableStacking, List<EffectConfig> effects,
                List<String> description, String helmet, String chestplate, String leggings, String boots,
                String sword, String bow, int quality) {
            this.attackDamage = attackDamage;
            this.defense = defense;
            this.health = health;
            this.speed = speed;
            this.jump = jump;
            this.vampire = vampire;
            this.infiniteDurability = infiniteDurability;
            this.effectType = effectType;
            this.color1 = color1;
            this.color2 = color2;
            this.color3 = color3;
            this.enableStacking = enableStacking;
            this.effects = effects != null ? effects : new ArrayList<>();
            this.description = description;
            this.helmet = helmet;
            this.chestplate = chestplate;
            this.leggings = leggings;
            this.boots = boots;
            this.sword = sword;
            this.bow = bow;
            this.quality = quality;
        }
    }

    /**
     * 检查玩家是否穿戴完整套装
     *
     * @param player 玩家
     * @return 套装名称，如果没有穿戴完整套装则返回null
     */
    public static String checkPlayerSuit(Player player) {
        ItemStack helmet = player.getInventory().getHelmet();
        ItemStack chestplate = player.getInventory().getChestplate();
        ItemStack leggings = player.getInventory().getLeggings();
        ItemStack boots = player.getInventory().getBoots();

        // 检查是否有装备
        if (helmet == null || chestplate == null || leggings == null || boots == null) {
            return null;
        }

        // 检查是否有显示名称
        if (!helmet.hasItemMeta() || !helmet.getItemMeta().hasDisplayName() ||
                !chestplate.hasItemMeta() || !chestplate.getItemMeta().hasDisplayName() ||
                !leggings.hasItemMeta() || !leggings.getItemMeta().hasDisplayName() ||
                !boots.hasItemMeta() || !boots.getItemMeta().hasDisplayName()) {
            return null;
        }

        String helmetName = helmet.getItemMeta().getDisplayName();
        String chestplateName = chestplate.getItemMeta().getDisplayName();
        String leggingsName = leggings.getItemMeta().getDisplayName();
        String bootsName = boots.getItemMeta().getDisplayName();

        // 遍历所有套装配置
        ConfigurationSection suitSection = Cuilian.Suit.getConfigurationSection("suit");
        if (suitSection == null) {
            return null;
        }

        for (String suitName : suitSection.getKeys(false)) {
            ConfigurationSection suit = suitSection.getConfigurationSection(suitName);
            if (suit == null)
                continue;

            ConfigurationSection items = suit.getConfigurationSection("item");
            if (items == null)
                continue;

            String requiredHead = items.getString("head");
            String requiredChest = items.getString("chest");
            String requiredLeg = items.getString("leg");
            String requiredFoot = items.getString("foot");

            // 检查装备名称是否匹配（支持颜色代码）
            if (matchItemName(helmetName, requiredHead) &&
                    matchItemName(chestplateName, requiredChest) &&
                    matchItemName(leggingsName, requiredLeg) &&
                    matchItemName(bootsName, requiredFoot)) {
                return suitName;
            }
        }

        return null;
    }

    /**
     * 匹配装备名称（忽略颜色代码）
     */
    private static boolean matchItemName(String actualName, String requiredName) {
        if (actualName == null || requiredName == null) {
            return false;
        }

        // 移除颜色代码进行比较
        String cleanActual = actualName.replaceAll("§[0-9a-fk-or]", "");
        String cleanRequired = requiredName.replaceAll("§[0-9a-fk-or]", "");

        return cleanActual.contains(cleanRequired) || cleanRequired.contains(cleanActual);
    }

    /**
     * 获取套装属性
     */
    public static SuitAttribute getSuitAttribute(String suitName) {
        if (suitName == null) {
            return null;
        }

        ConfigurationSection suitSection = Cuilian.Suit.getConfigurationSection("suit." + suitName);
        if (suitSection == null) {
            return null;
        }

        ConfigurationSection attribute = suitSection.getConfigurationSection("attribute");
        ConfigurationSection effect = suitSection.getConfigurationSection("effect");
        ConfigurationSection items = suitSection.getConfigurationSection("item");
        List<String> description = suitSection.getStringList("description");

        if (attribute == null || effect == null || items == null) {
            return null;
        }

        // 获取品质等级
        int quality = suitSection.getInt("quality", Cuilian.Suit.getInt("quality.default", 5));

        // 获取武器配置
        ConfigurationSection weapon = items.getConfigurationSection("weapon");
        String sword = weapon != null ? weapon.getString("sword", "剑") : "剑";
        String bow = weapon != null ? weapon.getString("bow", "弓") : "弓";

        // 解析特效配置
        boolean enableStacking = effect.getBoolean("enable_stacking", false);
        List<EffectConfig> effects = new ArrayList<>();

        // 检查是否有多特效配置
        if (effect.contains("effects")) {
            List<Map<?, ?>> effectsList = effect.getMapList("effects");
            for (Map<?, ?> effectMap : effectsList) {
                String type = (String) effectMap.get("type");
                Boolean enabled = (Boolean) effectMap.get("enabled");
                String colore1 = (String) effectMap.get("colore1");
                String colore2 = (String) effectMap.get("colore2");
                String colore3 = (String) effectMap.get("colore3");

                if (type != null) {
                    effects.add(new EffectConfig(
                            type,
                            enabled != null ? enabled : true,
                            colore1 != null ? colore1 : "红",
                            colore2 != null ? colore2 : "蓝",
                            colore3 != null ? colore3 : "绿"));
                }
            }
        }

        return new SuitAttribute(
                attribute.getInt("attack_damage", 0),
                attribute.getInt("defense", 0),
                attribute.getInt("health", 0),
                attribute.getDouble("speed", 0.0),
                attribute.getInt("jump", 0),
                attribute.getInt("vampire", 0),
                attribute.getBoolean("infinite_durability", false),
                effect.getString("type", "wings"),
                effect.getString("color1", "红"),
                effect.getString("color2", "蓝"),
                effect.getString("color3", "绿"),
                enableStacking,
                effects,
                description,
                items.getString("head", "头盔"),
                items.getString("chest", "胸甲"),
                items.getString("leg", "护腿"),
                items.getString("foot", "靴子"),
                sword,
                bow,
                quality);
    }

    /**
     * 更新玩家套装状态
     */
    public static void updatePlayerSuit(Player player) {
        String playerName = player.getName();
        String currentSuit = checkPlayerSuit(player);
        String previousSuit = playerSuits.get(playerName);

        // 如果套装没有变化，直接返回
        if (Objects.equals(currentSuit, previousSuit)) {
            return;
        }

        // 移除之前的套装效果
        if (previousSuit != null) {
            removeSuitEffects(player);
            Cuilian.lizi.remove(playerName);
        }

        // 应用新的套装效果
        if (currentSuit != null) {
            SuitAttribute attribute = getSuitAttribute(currentSuit);
            if (attribute != null) {
                applySuitEffects(player, attribute);
                playerSuits.put(playerName, currentSuit);
                playerSuitAttributes.put(playerName, attribute);

                // 检查全局特效开关和玩家个人特效设置
                boolean globalEffectsEnabled = Cuilian.config.getBoolean("effects", true);
                boolean playerEffectsEnabled = isPlayerEffectEnabled(playerName);

                if (globalEffectsEnabled && playerEffectsEnabled) {
                    // 全局和个人特效都启用，激活特效
                    Cuilian.lizi.put(playerName, -1); // 使用-1表示套装特效

                    // 发送消息
                    player.sendMessage("§a§l检测到完整套装: §e" + currentSuit);
                    player.sendMessage("§a§l套装特效已激活！");
                } else if (!globalEffectsEnabled) {
                    // 全局特效被禁用
                    player.sendMessage("§a§l检测到完整套装: §e" + currentSuit);
                    player.sendMessage("§c§l服务器已禁用特效显示");
                    player.sendMessage("§7§l套装属性正常生效，但无法显示特效");
                } else {
                    // 玩家个人特效被关闭
                    player.sendMessage("§a§l检测到完整套装: §e" + currentSuit);
                    player.sendMessage("§7§l套装特效已关闭，使用 §e/cuilian effect on §7开启");
                }

                // 显示套装描述
                if (attribute.description != null && !attribute.description.isEmpty()) {
                    for (String line : attribute.description) {
                        player.sendMessage(line);
                    }
                }

                // 显示特效信息
                displaySuitEffectInfo(player, attribute);
            }
        } else {
            // 没有套装，移除记录
            playerSuits.remove(playerName);
            playerSuitAttributes.remove(playerName);
        }
    }

    /**
     * 应用套装效果
     */
    private static void applySuitEffects(Player player, SuitAttribute attribute) {
        // 应用生命值加成
        if (attribute.health > 0) {
            double maxHealth = player.getMaxHealth() + attribute.health;
            player.setMaxHealth(Math.min(maxHealth, 2048.0)); // 限制最大生命值
        }

        // 应用速度加成
        if (attribute.speed > 0) {
            player.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, Integer.MAX_VALUE,
                    (int) (attribute.speed * 10), false, false), true);
        }

        // 应用跳跃加成
        if (attribute.jump > 0) {
            player.addPotionEffect(new PotionEffect(PotionEffectType.JUMP, Integer.MAX_VALUE,
                    attribute.jump - 1, false, false), true);
        }
    }

    /**
     * 移除套装效果
     */
    private static void removeSuitEffects(Player player) {
        // 移除药水效果
        player.removePotionEffect(PotionEffectType.SPEED);
        player.removePotionEffect(PotionEffectType.JUMP);

        // 重置生命值
        player.setMaxHealth(20.0);
    }

    /**
     * 获取玩家当前套装
     */
    public static String getPlayerSuit(String playerName) {
        return playerSuits.get(playerName);
    }

    /**
     * 获取玩家套装属性
     */
    public static SuitAttribute getPlayerSuitAttribute(String playerName) {
        return playerSuitAttributes.get(playerName);
    }

    /**
     * 玩家离开时清理数据
     */
    public static void cleanupPlayer(String playerName) {
        playerSuits.remove(playerName);
        playerSuitAttributes.remove(playerName);
        playerEffectEnabled.remove(playerName);
        playerAutoEquipPreference.remove(playerName);
    }

    /**
     * 检查玩家是否启用特效显示
     *
     * @param playerName 玩家名
     * @return 是否启用特效 (默认为true)
     */
    public static boolean isPlayerEffectEnabled(String playerName) {
        return playerEffectEnabled.getOrDefault(playerName, true);
    }

    /**
     * 设置玩家特效显示状态
     *
     * @param playerName 玩家名
     * @param enabled    是否启用特效
     */
    public static void setPlayerEffectEnabled(String playerName, boolean enabled) {
        playerEffectEnabled.put(playerName, enabled);

        // 如果关闭特效，立即从lizi列表中移除
        if (!enabled) {
            Cuilian.lizi.remove(playerName);
        } else {
            // 如果开启特效，重新检查套装状态
            Player player = Bukkit.getPlayer(playerName);
            if (player != null) {
                updatePlayerSuit(player);
            }
        }
    }

    /**
     * 切换玩家特效显示状态
     *
     * @param playerName 玩家名
     * @return 切换后的状态
     */
    public static boolean togglePlayerEffect(String playerName) {
        boolean currentState = isPlayerEffectEnabled(playerName);
        boolean newState = !currentState;
        setPlayerEffectEnabled(playerName, newState);
        return newState;
    }

    /**
     * 检查玩家是否偏好自动装备
     *
     * @param playerName 玩家名
     * @return 是否自动装备 (默认为false，即放入背包)
     */
    public static boolean isPlayerAutoEquipEnabled(String playerName) {
        return playerAutoEquipPreference.getOrDefault(playerName, false);
    }

    /**
     * 设置玩家自动装备偏好
     *
     * @param playerName 玩家名
     * @param autoEquip  是否自动装备
     */
    public static void setPlayerAutoEquipPreference(String playerName, boolean autoEquip) {
        playerAutoEquipPreference.put(playerName, autoEquip);
    }

    /**
     * 切换玩家自动装备偏好
     *
     * @param playerName 玩家名
     * @return 切换后的状态
     */
    public static boolean togglePlayerAutoEquip(String playerName) {
        boolean currentState = isPlayerAutoEquipEnabled(playerName);
        boolean newState = !currentState;
        setPlayerAutoEquipPreference(playerName, newState);
        return newState;
    }

    /**
     * 获取品质配置
     *
     * @param qualityLevel 品质等级
     * @return 品质配置
     */
    public static QualityConfig getQualityConfig(int qualityLevel) {
        ConfigurationSection qualitySection = Cuilian.Suit.getConfigurationSection("quality.levels." + qualityLevel);
        if (qualitySection == null) {
            // 如果找不到指定品质，返回默认品质
            int defaultQuality = Cuilian.Suit.getInt("quality.default", 5);
            qualitySection = Cuilian.Suit.getConfigurationSection("quality.levels." + defaultQuality);
            if (qualitySection == null) {
                // 如果连默认品质都找不到，返回一个基础品质
                return new QualityConfig("§7普通", "§7", "§7普通品质的装备");
            }
        }

        return new QualityConfig(
                qualitySection.getString("name", "§7普通"),
                qualitySection.getString("color", "§7"),
                qualitySection.getString("description", "§7普通品质的装备"));
    }

    /**
     * 获取所有可用套装列表
     */
    public static Set<String> getAllSuits() {
        ConfigurationSection suitSection = Cuilian.Suit.getConfigurationSection("suit");
        if (suitSection == null) {
            return new HashSet<>();
        }
        return suitSection.getKeys(false);
    }

    /**
     * 给予玩家套装装备
     *
     * @param player   目标玩家
     * @param suitName 套装名称
     * @return 是否成功给予
     */
    public static boolean giveSuitToPlayer(Player player, String suitName) {
        SuitAttribute attribute = getSuitAttribute(suitName);
        if (attribute == null) {
            return false;
        }

        try {
            // 创建套装装备
            ItemStack helmet = createSuitItem(Material.DIAMOND_HELMET, attribute.helmet, suitName, "head");
            ItemStack chestplate = createSuitItem(Material.DIAMOND_CHESTPLATE, attribute.chestplate, suitName, "chest");
            ItemStack leggings = createSuitItem(Material.DIAMOND_LEGGINGS, attribute.leggings, suitName, "leg");
            ItemStack boots = createSuitItem(Material.DIAMOND_BOOTS, attribute.boots, suitName, "foot");

            // 创建套装武器
            ItemStack sword = createSuitItem(Material.DIAMOND_SWORD, attribute.sword, suitName, "sword");
            ItemStack bow = createSuitItem(Material.BOW, attribute.bow, suitName, "bow");

            // 根据目标玩家的偏好决定给予方式
            if (isPlayerAutoEquipEnabled(player.getName())) {
                // 自动装备模式
                return autoEquipSuit(player, helmet, chestplate, leggings, boots, sword, bow);
            } else {
                // 背包模式
                return giveSuitToBag(player, helmet, chestplate, leggings, boots, sword, bow);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 自动装备套装
     */
    private static boolean autoEquipSuit(Player player, ItemStack helmet, ItemStack chestplate,
            ItemStack leggings, ItemStack boots, ItemStack sword, ItemStack bow) {
        try {
            // 保存旧装备
            ItemStack oldHelmet = player.getInventory().getHelmet();
            ItemStack oldChestplate = player.getInventory().getChestplate();
            ItemStack oldLeggings = player.getInventory().getLeggings();
            ItemStack oldBoots = player.getInventory().getBoots();

            // 装备新套装
            player.getInventory().setHelmet(helmet);
            player.getInventory().setChestplate(chestplate);
            player.getInventory().setLeggings(leggings);
            player.getInventory().setBoots(boots);

            // 处理旧装备
            handleOldEquipment(player, oldHelmet, oldChestplate, oldLeggings, oldBoots);

            // 将武器放入背包
            if (player.getInventory().firstEmpty() != -1) {
                player.getInventory().addItem(sword);
            } else {
                player.getWorld().dropItem(player.getLocation(), sword);
            }

            if (player.getInventory().firstEmpty() != -1) {
                player.getInventory().addItem(bow);
            } else {
                player.getWorld().dropItem(player.getLocation(), bow);
            }

            // 延迟检测套装状态，确保装备已经更新
            Bukkit.getScheduler().runTaskLater(Cuilian.getInstance(), new Runnable() {
                @Override
                public void run() {
                    updatePlayerSuit(player);
                }
            }, 1L); // 延迟1tick

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 将套装放入背包
     */
    private static boolean giveSuitToBag(Player player, ItemStack helmet, ItemStack chestplate,
            ItemStack leggings, ItemStack boots, ItemStack sword, ItemStack bow) {
        try {
            // 检查背包空间 - 计算真正的空格子数量
            int emptySlots = 0;
            for (int i = 0; i < 36; i++) { // 玩家背包有36个格子（不包括装备栏）
                ItemStack item = player.getInventory().getItem(i);
                if (item == null || item.getType() == Material.AIR) {
                    emptySlots++;
                }
            }

            // 需要6个空格子来放置套装（4件装备+2件武器）
            if (emptySlots < 6) {
                // 背包空间不足，发送错误消息并返回失败
                player.sendMessage("§c§l背包空间不足！需要至少6个空格子来放置套装和武器。");
                player.sendMessage("§c§l当前空格子数量: " + emptySlots + "/6");
                return false;
            }

            // 背包空间充足，直接添加到背包
            player.getInventory().addItem(helmet, chestplate, leggings, boots, sword, bow);
            player.sendMessage("§a§l套装和武器已放入背包！请手动穿戴以激活套装效果。");

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 处理旧装备
     */
    private static void handleOldEquipment(Player player, ItemStack... oldItems) {
        for (ItemStack oldItem : oldItems) {
            if (oldItem != null) {
                if (player.getInventory().firstEmpty() != -1) {
                    player.getInventory().addItem(oldItem);
                } else {
                    player.getWorld().dropItem(player.getLocation(), oldItem);
                }
            }
        }
    }

    /**
     * 检查玩家背包空间
     *
     * @param player 玩家
     * @return 空格子数量
     */
    public static int getPlayerEmptySlots(Player player) {
        int emptySlots = 0;
        for (int i = 0; i < 36; i++) { // 玩家背包有36个格子（不包括装备栏）
            ItemStack item = player.getInventory().getItem(i);
            if (item == null || item.getType() == Material.AIR) {
                emptySlots++;
            }
        }
        return emptySlots;
    }

    /**
     * 检查玩家是否有足够的背包空间来接收套装
     *
     * @param player 玩家
     * @return 是否有足够空间
     */
    public static boolean hasEnoughSpaceForSuit(Player player) {
        return getPlayerEmptySlots(player) >= 6; // 需要6个空格子（4件装备+2件武器）
    }

    /**
     * 创建套装装备物品
     *
     * @param material      装备材质
     * @param name          装备名称
     * @param suitName      套装名称
     * @param equipmentSlot 装备槽位 (head, chest, leg, foot)
     * @return 装备物品
     */
    private static ItemStack createSuitItem(Material material, String name, String suitName, String equipmentSlot) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();

        // 获取套装属性和品质配置
        SuitAttribute attribute = getSuitAttribute(suitName);
        if (attribute == null) {
            return item;
        }

        QualityConfig quality = getQualityConfig(attribute.quality);

        // 设置显示名称（使用品质颜色）
        meta.setDisplayName(quality.color + "§l" + name);

        // 设置描述
        List<String> lore = new ArrayList<>();
        lore.add("§7§l套装: §e§l" + suitName);
        lore.add("§7§l品质: " + quality.name);
        lore.add("§7§l" + quality.description);
        lore.add("");

        // 添加套装效果标题
        lore.add("§6§l套装效果:");
        lore.add("§6§l【" + suitName + "】");
        lore.add("§e传说中的" + suitName.replace("套装", "") + "装者套装");
        lore.add("§a§l套装效果:");

        // 添加套装属性描述（使用灰色，表示需要激活）
        if (attribute.attackDamage > 0) {
            lore.add("§7- 攻击伤害 +" + attribute.attackDamage);
        }
        if (attribute.defense > 0) {
            lore.add("§7- 防御力 +" + attribute.defense);
        }
        if (attribute.health > 0) {
            lore.add("§7- 生命值 +" + attribute.health);
        }
        if (attribute.speed > 0) {
            lore.add("§7- 移动速度 +" + (int) (attribute.speed * 100) + "%");
        }
        if (attribute.jump > 0) {
            lore.add("§7- 跳跃高度 +" + attribute.jump);
        }
        if (attribute.vampire > 0) {
            lore.add("§7- 吸血 +" + attribute.vampire + "%");
        }

        // 添加无限耐久信息
        if (attribute.infiniteDurability) {
            lore.add("§b- 无限耐久");
        }

        // 添加特效信息
        lore.add("§7- " + getEffectDisplayName(attribute.effectType) + "特效");

        lore.add("");
        lore.add("§c§l※ 需要穿戴完整套装才能激活效果");

        meta.setLore(lore);
        item.setItemMeta(meta);

        // 应用宝石强化
        int gemLevel = getGemLevel(suitName, equipmentSlot);
        if (gemLevel > 0) {
            item = GemIntegration.applyGemEnhancement(item, gemLevel);
        }

        return item;
    }

    /**
     * 获取套装装备的宝石等级
     *
     * @param suitName      套装名称
     * @param equipmentSlot 装备槽位
     * @return 宝石等级
     */
    public static int getGemLevel(String suitName, String equipmentSlot) {
        ConfigurationSection suitSection = Cuilian.Suit.getConfigurationSection("suit." + suitName);
        if (suitSection == null) {
            return 0;
        }

        ConfigurationSection gemLevels = suitSection.getConfigurationSection("gem_levels");
        if (gemLevels == null) {
            return 0;
        }

        return gemLevels.getInt(equipmentSlot, 0);
    }

    /**
     * 创建动态套装物品（根据玩家当前状态显示激活效果）
     *
     * @param material      装备材质
     * @param name          装备名称
     * @param suitName      套装名称
     * @param equipmentSlot 装备槽位
     * @param player        玩家（用于检测激活状态）
     * @return 装备物品
     */
    public static ItemStack createDynamicSuitItem(Material material, String name, String suitName,
            String equipmentSlot, Player player) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();

        // 获取套装属性和品质配置
        SuitAttribute attribute = getSuitAttribute(suitName);
        if (attribute == null) {
            return item;
        }

        QualityConfig quality = getQualityConfig(attribute.quality);

        // 设置显示名称（使用品质颜色）
        meta.setDisplayName(quality.color + "§l" + name);

        // 检查玩家是否激活了该套装
        boolean isActivated = player != null && suitName.equals(getPlayerSuit(player.getName()));

        // 设置描述
        List<String> lore = new ArrayList<>();
        lore.add("§7§l套装: §e§l" + suitName);
        lore.add("§7§l品质: " + quality.name);
        lore.add("§7§l" + quality.description);
        lore.add("");

        // 添加套装效果标题
        lore.add("§6§l套装效果:");
        lore.add("§6§l【" + suitName + "】");
        lore.add("§e传说中的" + suitName.replace("套装", "") + "装者套装");

        // 从配置文件获取显示配置
        DisplayConfig displayConfig = getDisplayConfig(suitName, isActivated);

        // 根据激活状态显示不同颜色的套装效果
        lore.add(displayConfig.statusText);

        // 添加套装属性描述（每种属性使用不同颜色）
        if (attribute.attackDamage > 0) {
            lore.add(displayConfig.attackDamageColor + "- 攻击伤害 +" + attribute.attackDamage);
        }
        if (attribute.defense > 0) {
            lore.add(displayConfig.defenseColor + "- 防御力 +" + attribute.defense);
        }
        if (attribute.health > 0) {
            lore.add(displayConfig.healthColor + "- 生命值 +" + attribute.health);
        }
        if (attribute.speed > 0) {
            lore.add(displayConfig.speedColor + "- 移动速度 +" + (int) (attribute.speed * 100) + "%");
        }
        if (attribute.jump > 0) {
            lore.add(displayConfig.jumpColor + "- 跳跃高度 +" + attribute.jump);
        }
        if (attribute.vampire > 0) {
            lore.add(displayConfig.vampireColor + "- 吸血 +" + attribute.vampire + "%");
        }

        // 添加淬炼属性信息（如果玩家有淬炼等级）
        if (player != null) {
            addCuilianAttributes(lore, player, isActivated);
        }

        // 添加无限耐久信息
        if (attribute.infiniteDurability) {
            lore.add(isActivated ? "§b- 无限耐久" : "§7- 无限耐久");
        }

        // 添加特效信息
        lore.add(displayConfig.effectColor + "- " + getEffectDisplayName(attribute.effectType) + "特效");

        lore.add("");

        // 根据激活状态显示不同的提示信息
        lore.add(displayConfig.tipText);

        meta.setLore(lore);
        item.setItemMeta(meta);

        // 应用宝石强化
        int gemLevel = getGemLevel(suitName, equipmentSlot);
        if (gemLevel > 0) {
            item = GemIntegration.applyGemEnhancement(item, gemLevel);
        }

        return item;
    }

    /**
     * 显示套装特效信息到聊天栏
     *
     * @param player    玩家
     * @param attribute 套装属性
     */
    private static void displaySuitEffectInfo(Player player, SuitAttribute attribute) {
        try {
            boolean hasEffectInfo = false;

            // 显示特效信息标题
            player.sendMessage("§6§l━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            player.sendMessage("§e§l✨ 套装特效信息:");

            // 检查新格式的特效配置
            if (attribute.effects != null && !attribute.effects.isEmpty()) {
                hasEffectInfo = true;

                // 检查是否启用叠加特效
                if (attribute.enableStacking && attribute.effects.size() > 1) {
                    player.sendMessage("§a§l📋 叠加特效 §7(共 §e" + attribute.effects.size() + " §7种):");

                    for (int i = 0; i < attribute.effects.size(); i++) {
                        EffectConfig effect = attribute.effects.get(i);
                        String displayName = getEffectDisplayName(effect.type);
                        String statusColor = effect.enabled ? "§a" : "§c";
                        String statusText = effect.enabled ? "启用" : "禁用";

                        player.sendMessage(String.format("§7  %d. %s%s §7[%s%s§7] §8(%s,%s,%s)",
                                i + 1, statusColor, displayName, statusColor, statusText,
                                effect.colore1, effect.colore2, effect.colore3));
                    }
                } else {
                    // 单一特效
                    EffectConfig effect = attribute.effects.get(0);
                    String displayName = getEffectDisplayName(effect.type);
                    String statusColor = effect.enabled ? "§a" : "§c";
                    String statusText = effect.enabled ? "启用" : "禁用";

                    player.sendMessage("§a§l🎆 单一特效:");
                    player.sendMessage(String.format("§7  %s%s §7[%s%s§7] §8(%s,%s,%s)",
                            statusColor, displayName, statusColor, statusText,
                            effect.colore1, effect.colore2, effect.colore3));
                }
            } else {
                // 检查旧格式的特效配置
                String suitName = getPlayerSuit(player.getName());
                if (suitName != null) {
                    String basePath = "suit." + suitName + ".effect";
                    if (Cuilian.Suit.contains(basePath + ".type")) {
                        hasEffectInfo = true;

                        String type = Cuilian.Suit.getString(basePath + ".type");
                        String color1 = Cuilian.Suit.getString(basePath + ".color1", "");
                        String color2 = Cuilian.Suit.getString(basePath + ".color2", "");
                        String color3 = Cuilian.Suit.getString(basePath + ".color3", "");

                        String displayName = getEffectDisplayName(type);

                        player.sendMessage("§a§l🎆 单一特效:");
                        player.sendMessage(String.format("§7  §a%s §7[§a启用§7]", displayName));

                        if (!color1.isEmpty() || !color2.isEmpty() || !color3.isEmpty()) {
                            player.sendMessage(String.format("§7  颜色配置: §f%s§7, §f%s§7, §f%s",
                                    color1.isEmpty() ? "无" : color1,
                                    color2.isEmpty() ? "无" : color2,
                                    color3.isEmpty() ? "无" : color3));
                        }
                    }
                }
            }

            if (!hasEffectInfo) {
                player.sendMessage("§7§l该套装暂无特效配置");
            }

            // 显示宝石等级信息
            displayGemLevelInfo(player, attribute);

            player.sendMessage("§6§l━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        } catch (Exception e) {
            System.err.println("显示套装特效信息时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 显示宝石等级信息
     *
     * @param player    玩家
     * @param attribute 套装属性
     */
    private static void displayGemLevelInfo(Player player, SuitAttribute attribute) {
        try {
            // 检查玩家当前装备的宝石等级
            boolean hasGemInfo = false;
            StringBuilder gemInfo = new StringBuilder();

            // 检查头盔
            ItemStack helmet = player.getInventory().getHelmet();
            if (helmet != null) {
                int gemLevel = cn.winde.cuilian.suit.GemIntegration.getEnhancementLevel(helmet);
                if (gemLevel > 0) {
                    if (!hasGemInfo) {
                        gemInfo.append("§d§l💎 宝石强化等级:");
                        hasGemInfo = true;
                    }
                    gemInfo.append("\n§7  头盔: §b+").append(gemLevel).append(" §7级");
                }
            }

            // 检查胸甲
            ItemStack chestplate = player.getInventory().getChestplate();
            if (chestplate != null) {
                int gemLevel = cn.winde.cuilian.suit.GemIntegration.getEnhancementLevel(chestplate);
                if (gemLevel > 0) {
                    if (!hasGemInfo) {
                        gemInfo.append("§d§l💎 宝石强化等级:");
                        hasGemInfo = true;
                    }
                    gemInfo.append("\n§7  胸甲: §b+").append(gemLevel).append(" §7级");
                }
            }

            // 检查护腿
            ItemStack leggings = player.getInventory().getLeggings();
            if (leggings != null) {
                int gemLevel = cn.winde.cuilian.suit.GemIntegration.getEnhancementLevel(leggings);
                if (gemLevel > 0) {
                    if (!hasGemInfo) {
                        gemInfo.append("§d§l💎 宝石强化等级:");
                        hasGemInfo = true;
                    }
                    gemInfo.append("\n§7  护腿: §b+").append(gemLevel).append(" §7级");
                }
            }

            // 检查靴子
            ItemStack boots = player.getInventory().getBoots();
            if (boots != null) {
                int gemLevel = cn.winde.cuilian.suit.GemIntegration.getEnhancementLevel(boots);
                if (gemLevel > 0) {
                    if (!hasGemInfo) {
                        gemInfo.append("§d§l💎 宝石强化等级:");
                        hasGemInfo = true;
                    }
                    gemInfo.append("\n§7  靴子: §b+").append(gemLevel).append(" §7级");
                }
            }

            // 检查主手武器
            ItemStack weapon = player.getItemInHand();
            if (weapon != null && weapon.getType() != org.bukkit.Material.AIR) {
                int gemLevel = cn.winde.cuilian.suit.GemIntegration.getEnhancementLevel(weapon);
                if (gemLevel > 0) {
                    if (!hasGemInfo) {
                        gemInfo.append("§d§l💎 宝石强化等级:");
                        hasGemInfo = true;
                    }
                    gemInfo.append("\n§7  武器: §b+").append(gemLevel).append(" §7级");
                }
            }

            if (hasGemInfo) {
                player.sendMessage(gemInfo.toString());
            }

        } catch (Exception e) {
            System.err.println("显示宝石等级信息时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取特效显示名称
     *
     * @param effectType 特效类型
     * @return 显示名称
     */
    public static String getEffectDisplayName(String effectType) {
        switch (effectType.toLowerCase()) {
            // 淬炼5.6版本经典特效
            case "flame_basic":
                return "基础火焰";
            case "footstep":
                return "脚步";
            case "spiral_flame":
                return "螺旋火焰";
            case "rotating_halo":
                return "旋转光环";
            case "four_direction_spell":
                return "四方向法术";
            case "wings_56":
                return "5.6版本翅膀";
            // 现代版本基础特效
            case "wings":
                return "翅膀";
            case "halo":
                return "光环";
            case "flame":
                return "火焰";
            case "nebula":
                return "星云";
            case "tornado":
                return "龙卷风";
            case "stars":
                return "星星";
            // 现代版本几何特效
            case "spiral":
                return "螺旋";
            case "sphere":
                return "球体";
            case "orbit":
                return "轨道";
            case "cube":
                return "立方体";
            case "helix":
                return "螺旋桨";
            // 现代版本动态特效
            case "batman":
                return "蝙蝠侠";
            case "popper":
                return "爆炸螺旋";
            case "pulse":
                return "脉冲";
            case "whirl":
                return "旋涡";
            case "whirlwind":
                return "旋风";
            case "invocation":
                return "召唤法阵";
            // 现代版本高级特效
            case "lightning":
                return "雷电";
            case "rainbow":
                return "彩虹";
            case "spacerift":
                return "时空裂缝";
            case "frost":
                return "冰霜";
            case "shadow":
                return "暗影";
            // PlayerParticles基础特效
            case "pp_wings":
                return "PP翅膀";
            case "pp_halo":
                return "PP光环";
            case "pp_spiral":
                return "PP螺旋";
            case "pp_orbit":
                return "PP轨道";
            case "pp_sphere":
                return "PP球体";
            // PlayerParticles高级特效
            case "pp_beam":
                return "PP光束";
            case "pp_cube":
                return "PP立方体";
            case "pp_quadhelix":
                return "PP四重螺旋";
            case "pp_spin":
                return "PP旋转";
            case "pp_thick":
                return "PP浓密";
            case "pp_feet":
                return "PP脚步";
            case "pp_point":
                return "PP光点";
            case "pp_arrows":
                return "PP箭矢";
            case "pp_swords":
                return "PP剑击";
            case "pp_move":
                return "PP移动";
            case "pp_blockbreak":
                return "PP破坏";
            case "pp_hurt":
                return "PP受伤";
            default:
                return effectType;
        }
    }

    /**
     * 从配置文件获取显示配置
     *
     * @param suitName    套装名称
     * @param isActivated 是否激活
     * @return 显示配置
     */
    private static DisplayConfig getDisplayConfig(String suitName, boolean isActivated) {
        String section = isActivated ? "activated" : "deactivated";
        String basePath = "suit." + suitName + ".display." + section;

        // 获取每种属性的颜色配置，如果套装没有配置则使用默认值
        String attackDamageColor = Cuilian.Suit.getString(basePath + ".attack_damage_color", isActivated ? "§c" : "§7");
        String defenseColor = Cuilian.Suit.getString(basePath + ".defense_color", isActivated ? "§9" : "§7");
        String healthColor = Cuilian.Suit.getString(basePath + ".health_color", isActivated ? "§a" : "§7");
        String speedColor = Cuilian.Suit.getString(basePath + ".speed_color", isActivated ? "§f" : "§7");
        String jumpColor = Cuilian.Suit.getString(basePath + ".jump_color", isActivated ? "§e" : "§7");
        String vampireColor = Cuilian.Suit.getString(basePath + ".vampire_color", isActivated ? "§d" : "§7");
        String effectColor = Cuilian.Suit.getString(basePath + ".effect_color", isActivated ? "§b" : "§7");

        String statusText = Cuilian.Suit.getString(basePath + ".status_text",
                isActivated ? "§a§l套装效果: §2§l✓ 已激活" : "§a§l套装效果: §7§l✗ 未激活");
        String tipText = Cuilian.Suit.getString(basePath + ".tip_text",
                isActivated ? "§2§l✓ 套装效果已激活！" : "§c§l※ 需要穿戴完整套装才能激活效果");

        return new DisplayConfig(attackDamageColor, defenseColor, healthColor, speedColor,
                jumpColor, vampireColor, effectColor, statusText, tipText);
    }

    /**
     * 显示配置类
     */
    private static class DisplayConfig {
        public final String attackDamageColor;
        public final String defenseColor;
        public final String healthColor;
        public final String speedColor;
        public final String jumpColor;
        public final String vampireColor;
        public final String effectColor;
        public final String statusText;
        public final String tipText;

        public DisplayConfig(String attackDamageColor, String defenseColor, String healthColor,
                String speedColor, String jumpColor, String vampireColor, String effectColor,
                String statusText, String tipText) {
            this.attackDamageColor = attackDamageColor;
            this.defenseColor = defenseColor;
            this.healthColor = healthColor;
            this.speedColor = speedColor;
            this.jumpColor = jumpColor;
            this.vampireColor = vampireColor;
            this.effectColor = effectColor;
            this.statusText = statusText;
            this.tipText = tipText;
        }
    }

    /**
     * 检查玩家的套装效果是否激活
     *
     * @param playerName 玩家名称
     * @return 套装效果激活状态
     */
    public static SuitEffectStatus getSuitEffectStatus(String playerName) {
        // 检查命名套装
        String namedSuit = getPlayerSuit(playerName);
        if (namedSuit != null) {
            SuitAttribute attribute = getSuitAttribute(namedSuit);
            if (attribute != null) {
                boolean effectEnabled = isPlayerEffectEnabled(playerName);
                boolean hasParticleEffect = Cuilian.lizi.containsKey(playerName);
                return new SuitEffectStatus(namedSuit, true, effectEnabled, hasParticleEffect, attribute);
            }
        }

        // 检查淬炼等级套装
        Player player = Cuilian.getInstance().getServer().getPlayer(playerName);
        if (player != null) {
            int suitLevel = Cuilian.checkPlayerZBCL(player);
            if (suitLevel >= 6) {
                boolean effectEnabled = isPlayerEffectEnabled(playerName);
                boolean hasParticleEffect = Cuilian.lizi.containsKey(playerName);
                return new SuitEffectStatus("淬炼" + suitLevel + "星套装", true, effectEnabled, hasParticleEffect, null);
            }
        }

        return new SuitEffectStatus(null, false, false, false, null);
    }

    /**
     * 套装效果状态类
     */
    public static class SuitEffectStatus {
        public final String suitName;
        public final boolean hasSuit;
        public final boolean effectEnabled;
        public final boolean particleActive;
        public final SuitAttribute attribute;

        public SuitEffectStatus(String suitName, boolean hasSuit, boolean effectEnabled,
                boolean particleActive, SuitAttribute attribute) {
            this.suitName = suitName;
            this.hasSuit = hasSuit;
            this.effectEnabled = effectEnabled;
            this.particleActive = particleActive;
            this.attribute = attribute;
        }
    }
}
