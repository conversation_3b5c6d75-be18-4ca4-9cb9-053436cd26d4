package cn.winde.cuilian.texture;

import org.bukkit.Material;
import org.bukkit.plugin.java.JavaPlugin;

import javax.swing.ImageIcon;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * 装备贴图管理器
 * 负责在插件启动时预加载所有装备贴图
 */
public class TextureManager {
    private static final Map<Material, ImageIcon> textureCache = new HashMap<>();
    private static final Map<Material, String> textureFileMap = new HashMap<>();
    private static Logger logger;
    private static File textureDirectory;
    private static boolean initialized = false;
    private static ImageIcon enchantmentGlintTexture = null;
    private static BufferedImage[] enchantmentGlintFrames = null;
    private static int currentGlintFrame = 0;
    private static long lastFrameTime = 0;
    private static int frameTime = 8; // 默认帧时间（游戏刻）

    /**
     * 初始化贴图管理器
     */
    public static void initialize(JavaPlugin plugin) {
        logger = plugin.getLogger();
        textureDirectory = new File(plugin.getDataFolder(), "textures");

        logger.info("初始化装备贴图管理器...");
        logger.info("贴图目录: " + textureDirectory.getAbsolutePath());

        // 确保贴图目录存在
        if (!textureDirectory.exists()) {
            textureDirectory.mkdirs();
            logger.info("创建贴图目录: " + textureDirectory.getAbsolutePath());
        }

        // 初始化贴图文件映射
        initializeTextureMapping();

        // 从插件资源中复制默认贴图
        copyDefaultTextures(plugin);

        // 扫描并添加实际存在的贴图文件
        scanExistingTextures();

        // 预加载所有贴图
        preloadAllTextures();

        // 加载附魔光效贴图
        loadEnchantmentGlint();

        initialized = true;
        logger.info("装备贴图管理器初始化完成！");
    }

    /**
     * 初始化贴图文件映射
     * 根据您提供的完整贴图集合，映射所有可能的物品
     */
    private static void initializeTextureMapping() {
        // 装备类 - 钻石
        addTextureMapping(Material.DIAMOND_HELMET, "diamond_helmet");
        addTextureMapping(Material.DIAMOND_CHESTPLATE, "diamond_chestplate");
        addTextureMapping(Material.DIAMOND_LEGGINGS, "diamond_leggings");
        addTextureMapping(Material.DIAMOND_BOOTS, "diamond_boots");
        addTextureMapping(Material.DIAMOND_SWORD, "diamond_sword");
        addTextureMapping(Material.DIAMOND_PICKAXE, "diamond_pickaxe");
        addTextureMapping(Material.DIAMOND_AXE, "diamond_axe");
        addTextureMapping(Material.DIAMOND_SPADE, "diamond_shovel");
        addTextureMapping(Material.DIAMOND_HOE, "diamond_hoe");

        // 装备类 - 铁
        addTextureMapping(Material.IRON_HELMET, "iron_helmet");
        addTextureMapping(Material.IRON_CHESTPLATE, "iron_chestplate");
        addTextureMapping(Material.IRON_LEGGINGS, "iron_leggings");
        addTextureMapping(Material.IRON_BOOTS, "iron_boots");
        addTextureMapping(Material.IRON_SWORD, "iron_sword");
        addTextureMapping(Material.IRON_PICKAXE, "iron_pickaxe");
        addTextureMapping(Material.IRON_AXE, "iron_axe");
        addTextureMapping(Material.IRON_SPADE, "iron_shovel");
        addTextureMapping(Material.IRON_HOE, "iron_hoe");

        // 装备类 - 金
        addTextureMapping(Material.GOLD_HELMET, "golden_helmet");
        addTextureMapping(Material.GOLD_CHESTPLATE, "golden_chestplate");
        addTextureMapping(Material.GOLD_LEGGINGS, "golden_leggings");
        addTextureMapping(Material.GOLD_BOOTS, "golden_boots");
        addTextureMapping(Material.GOLD_SWORD, "golden_sword");
        addTextureMapping(Material.GOLD_PICKAXE, "golden_pickaxe");
        addTextureMapping(Material.GOLD_AXE, "golden_axe");
        addTextureMapping(Material.GOLD_SPADE, "golden_shovel");
        addTextureMapping(Material.GOLD_HOE, "golden_hoe");

        // 装备类 - 皮革
        addTextureMapping(Material.LEATHER_HELMET, "leather_helmet");
        addTextureMapping(Material.LEATHER_CHESTPLATE, "leather_chestplate");
        addTextureMapping(Material.LEATHER_LEGGINGS, "leather_leggings");
        addTextureMapping(Material.LEATHER_BOOTS, "leather_boots");

        // 装备类 - 锁链
        addTextureMapping(Material.CHAINMAIL_HELMET, "chainmail_helmet");
        addTextureMapping(Material.CHAINMAIL_CHESTPLATE, "chainmail_chestplate");
        addTextureMapping(Material.CHAINMAIL_LEGGINGS, "chainmail_leggings");
        addTextureMapping(Material.CHAINMAIL_BOOTS, "chainmail_boots");

        // 装备类 - 石制
        addTextureMapping(Material.STONE_SWORD, "stone_sword");
        addTextureMapping(Material.STONE_PICKAXE, "stone_pickaxe");
        addTextureMapping(Material.STONE_AXE, "stone_axe");
        addTextureMapping(Material.STONE_SPADE, "stone_shovel");
        addTextureMapping(Material.STONE_HOE, "stone_hoe");

        // 装备类 - 木制
        addTextureMapping(Material.WOOD_SWORD, "wooden_sword");
        addTextureMapping(Material.WOOD_PICKAXE, "wooden_pickaxe");
        addTextureMapping(Material.WOOD_AXE, "wooden_axe");
        addTextureMapping(Material.WOOD_SPADE, "wooden_shovel");
        addTextureMapping(Material.WOOD_HOE, "wooden_hoe");

        // 特殊武器和工具
        addTextureMapping(Material.BOW, "bow");
        addTextureMapping(Material.FISHING_ROD, "fishing_rod");
        addTextureMapping(Material.FLINT_AND_STEEL, "flint_and_steel");
        addTextureMapping(Material.SHEARS, "shears");

        // 尝试添加新版本物品（如果服务器版本支持）
        addNewVersionItems();

        // 添加常见物品
        addCommonItems();

        logger.info("已注册 " + textureFileMap.size() + " 个物品贴图映射");
    }

    /**
     * 添加贴图映射，支持多种文件格式
     */
    private static void addTextureMapping(Material material, String baseName) {
        // 支持的图片格式
        String[] extensions = { ".png", ".jpg", ".jpeg", ".gif" };

        for (String ext : extensions) {
            String fileName = baseName + ext;
            textureFileMap.put(material, fileName);
            break; // 只添加第一个找到的格式
        }
    }

    /**
     * 添加新版本物品（1.9+）
     * 注意：由于这是1.8.8版本，这些物品都不存在，但保留方法以防将来升级
     */
    private static void addNewVersionItems() {
        // 1.8.8版本不支持任何1.9+的物品
        // 这个方法保留为空，以防将来需要升级到更高版本
        logger.info("跳过新版本物品映射（当前为1.8.8版本）");
    }

    /**
     * 添加常见物品 - 基于现有图片文件更新
     */
    private static void addCommonItems() {
        // 食物类 - 基于现有文件
        addTextureMapping(Material.APPLE, "apple");
        addTextureMapping(Material.BREAD, "bread");
        addTextureMapping(Material.COOKED_BEEF, "cooked_beef");
        addTextureMapping(Material.COOKED_CHICKEN, "cooked_chicken");
        addTextureMapping(Material.GRILLED_PORK, "cooked_porkchop"); // 1.8中熟猪肉的Material名称
        addTextureMapping(Material.GOLDEN_APPLE, "golden_apple");
        addTextureMapping(Material.GOLDEN_CARROT, "golden_carrot");
        addTextureMapping(Material.CAKE, "cake");
        addTextureMapping(Material.COOKIE, "cookie");
        addTextureMapping(Material.MELON, "melon_slice");
        addTextureMapping(Material.CARROT_ITEM, "carrot");
        addTextureMapping(Material.POTATO_ITEM, "potato");
        addTextureMapping(Material.BAKED_POTATO, "baked_potato");
        addTextureMapping(Material.RAW_BEEF, "beef");
        addTextureMapping(Material.RAW_CHICKEN, "chicken");
        addTextureMapping(Material.PORK, "porkchop");
        addTextureMapping(Material.MUSHROOM_SOUP, "mushroom_stew");
        addTextureMapping(Material.PUMPKIN_PIE, "pumpkin_pie");
        addTextureMapping(Material.ROTTEN_FLESH, "rotten_flesh");
        addTextureMapping(Material.SPIDER_EYE, "spider_eye");
        addTextureMapping(Material.POISONOUS_POTATO, "poisonous_potato");

        // 材料类 - 基于现有文件
        addTextureMapping(Material.DIAMOND, "diamond");
        addTextureMapping(Material.EMERALD, "emerald");
        addTextureMapping(Material.GOLD_INGOT, "gold_ingot");
        addTextureMapping(Material.IRON_INGOT, "iron_ingot");
        addTextureMapping(Material.COAL, "coal");
        addTextureMapping(Material.COAL, "charcoal"); // 1.8中木炭和煤炭是同一个Material
        addTextureMapping(Material.REDSTONE, "redstone");
        addTextureMapping(Material.INK_SACK, "lapis_lazuli"); // 1.8中青金石是INK_SACK的数据值
        addTextureMapping(Material.INK_SACK, "ink_sac"); // 1.8中墨囊也是INK_SACK
        addTextureMapping(Material.INK_SACK, "bone_meal"); // 1.8中骨粉也是INK_SACK的数据值
        addTextureMapping(Material.INK_SACK, "cocoa_beans"); // 1.8中可可豆也是INK_SACK的数据值
        addTextureMapping(Material.QUARTZ, "quartz");
        addTextureMapping(Material.STICK, "stick");
        addTextureMapping(Material.STRING, "string");
        addTextureMapping(Material.FEATHER, "feather");
        addTextureMapping(Material.LEATHER, "leather");
        addTextureMapping(Material.PAPER, "paper");
        addTextureMapping(Material.BOOK, "book");
        addTextureMapping(Material.SLIME_BALL, "slime_ball");
        addTextureMapping(Material.ENDER_PEARL, "ender_pearl");
        addTextureMapping(Material.BLAZE_ROD, "blaze_rod");
        addTextureMapping(Material.BLAZE_POWDER, "blaze_powder");
        addTextureMapping(Material.GHAST_TEAR, "ghast_tear");
        addTextureMapping(Material.NETHER_STAR, "nether_star");
        addTextureMapping(Material.NETHER_BRICK_ITEM, "nether_brick");
        addTextureMapping(Material.NETHER_STALK, "nether_wart");
        addTextureMapping(Material.BONE, "bone");
        addTextureMapping(Material.CLAY_BALL, "clay_ball");
        addTextureMapping(Material.CLAY_BRICK, "brick");
        addTextureMapping(Material.GOLD_NUGGET, "gold_nugget");
        addTextureMapping(Material.SULPHUR, "gunpowder"); // 1.8中火药的Material名称
        addTextureMapping(Material.SUGAR, "sugar");
        addTextureMapping(Material.WHEAT, "wheat");
        addTextureMapping(Material.SEEDS, "wheat_seeds"); // 1.8中小麦种子的Material名称
        addTextureMapping(Material.MELON_SEEDS, "melon_seeds");
        addTextureMapping(Material.PUMPKIN_SEEDS, "pumpkin_seeds");
        addTextureMapping(Material.FLINT, "flint");
        addTextureMapping(Material.GLOWSTONE_DUST, "glowstone_dust");
        addTextureMapping(Material.MAGMA_CREAM, "magma_cream");
        addTextureMapping(Material.EYE_OF_ENDER, "ender_eye");

        // 工具和实用物品 - 基于现有文件
        addTextureMapping(Material.BUCKET, "bucket");
        addTextureMapping(Material.WATER_BUCKET, "water_bucket");
        addTextureMapping(Material.LAVA_BUCKET, "lava_bucket");
        addTextureMapping(Material.MILK_BUCKET, "milk_bucket");
        addTextureMapping(Material.COMPASS, "compass_00");
        addTextureMapping(Material.WATCH, "clock_00");
        addTextureMapping(Material.EMPTY_MAP, "map"); // 1.8中空地图的Material名称
        addTextureMapping(Material.NAME_TAG, "name_tag");
        addTextureMapping(Material.SADDLE, "saddle");
        addTextureMapping(Material.CARROT_STICK, "carrot_on_a_stick"); // 1.8中胡萝卜钓竿的Material名称
        addTextureMapping(Material.PAINTING, "painting");
        addTextureMapping(Material.ITEM_FRAME, "item_frame");
        addTextureMapping(Material.FLOWER_POT_ITEM, "flower_pot"); // 1.8中花盆物品的Material名称
        addTextureMapping(Material.BOWL, "bowl");

        // 药水和附魔 - 基于现有文件
        addTextureMapping(Material.POTION, "potion");
        addTextureMapping(Material.GLASS_BOTTLE, "glass_bottle");
        addTextureMapping(Material.ENCHANTED_BOOK, "enchanted_book");
        addTextureMapping(Material.EXP_BOTTLE, "experience_bottle");

        // 箭矢和投掷物 - 基于现有文件
        addTextureMapping(Material.ARROW, "arrow");
        addTextureMapping(Material.SNOW_BALL, "snowball");
        addTextureMapping(Material.EGG, "egg");

        // 矿车类 - 基于现有文件
        addTextureMapping(Material.MINECART, "minecart");
        addTextureMapping(Material.STORAGE_MINECART, "chest_minecart"); // 1.8中运输矿车的Material名称
        addTextureMapping(Material.POWERED_MINECART, "furnace_minecart"); // 1.8中动力矿车的Material名称
        addTextureMapping(Material.EXPLOSIVE_MINECART, "tnt_minecart"); // 1.8中TNT矿车的Material名称
        addTextureMapping(Material.HOPPER_MINECART, "hopper_minecart");

        // 音乐唱片 - 基于现有文件（1.8.8版本的Material名称）
        addTextureMapping(Material.GOLD_RECORD, "music_disc_13"); // 1.8中13号唱片
        addTextureMapping(Material.GREEN_RECORD, "music_disc_cat"); // 1.8中cat唱片
        addTextureMapping(Material.RECORD_3, "music_disc_blocks"); // 1.8中blocks唱片
        addTextureMapping(Material.RECORD_4, "music_disc_chirp"); // 1.8中chirp唱片
        addTextureMapping(Material.RECORD_5, "music_disc_far"); // 1.8中far唱片
        addTextureMapping(Material.RECORD_6, "music_disc_mall"); // 1.8中mall唱片
        addTextureMapping(Material.RECORD_7, "music_disc_mellohi"); // 1.8中mellohi唱片
        addTextureMapping(Material.RECORD_8, "music_disc_stal"); // 1.8中stal唱片
        addTextureMapping(Material.RECORD_9, "music_disc_strad"); // 1.8中strad唱片
        addTextureMapping(Material.RECORD_10, "music_disc_ward"); // 1.8中ward唱片
        addTextureMapping(Material.RECORD_11, "music_disc_11"); // 1.8中11号唱片
        addTextureMapping(Material.RECORD_12, "music_disc_wait"); // 1.8中wait唱片

        // 特殊物品 - 基于现有文件
        addTextureMapping(Material.BOAT, "oak_boat"); // 1.8中只有橡木船
        addTextureMapping(Material.WOOD_DOOR, "oak_door"); // 1.8中只有橡木门
        addTextureMapping(Material.SIGN, "oak_sign"); // 1.8中只有橡木告示牌
        addTextureMapping(Material.IRON_DOOR, "iron_door"); // 1.8中铁门物品的Material名称
        addTextureMapping(Material.BREWING_STAND_ITEM, "brewing_stand"); // 1.8中酿造台物品的Material名称
        addTextureMapping(Material.CAULDRON_ITEM, "cauldron"); // 1.8中炼药锅物品的Material名称
        addTextureMapping(Material.REDSTONE_COMPARATOR, "comparator"); // 1.8中红石比较器的Material名称
        addTextureMapping(Material.HOPPER, "hopper"); // 1.8中漏斗的Material名称

        // 马铠 - 基于现有文件（1.8.8版本的Material名称）
        addTextureMapping(Material.IRON_BARDING, "iron_horse_armor"); // 1.8中铁马铠的Material名称
        addTextureMapping(Material.GOLD_BARDING, "golden_horse_armor"); // 1.8中金马铠的Material名称
        addTextureMapping(Material.DIAMOND_BARDING, "diamond_horse_armor"); // 1.8中钻石马铠的Material名称
        // 注意：1.8.8中可能没有皮革马铠，如果有错误会被try-catch捕获
    }

    /**
     * 扫描贴图目录中实际存在的文件，自动添加映射
     */
    private static void scanExistingTextures() {
        if (!textureDirectory.exists()) {
            return;
        }

        File[] files = textureDirectory.listFiles((dir, name) -> {
            String lowerName = name.toLowerCase();
            return lowerName.endsWith(".png") || lowerName.endsWith(".jpg") ||
                    lowerName.endsWith(".jpeg") || lowerName.endsWith(".gif");
        });

        if (files == null) {
            return;
        }

        int addedCount = 0;
        for (File file : files) {
            String fileName = file.getName();
            String baseName = fileName.substring(0, fileName.lastIndexOf('.'));

            // 尝试匹配材质名称
            Material material = findMaterialByName(baseName);
            if (material != null && !textureFileMap.containsKey(material)) {
                textureFileMap.put(material, fileName);
                addedCount++;
                logger.info("自动发现贴图: " + fileName + " -> " + material.name());
            }
        }

        if (addedCount > 0) {
            logger.info("自动添加了 " + addedCount + " 个贴图映射");
        }
    }

    /**
     * 根据文件名查找对应的材质
     */
    private static Material findMaterialByName(String baseName) {
        // 标准化文件名（转换为大写，替换分隔符）
        String materialName = baseName.toUpperCase().replace("-", "_");

        try {
            // 直接尝试匹配材质名称
            return Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            // 如果直接匹配失败，尝试一些常见的变体
            String[] variants = {
                    materialName,
                    materialName.replace("_", ""),
                    materialName + "_ITEM",
                    "ITEM_" + materialName
            };

            for (String variant : variants) {
                try {
                    return Material.valueOf(variant);
                } catch (IllegalArgumentException ignored) {
                    // 继续尝试下一个变体
                }
            }
        }

        return null; // 找不到对应的材质
    }

    /**
     * 从插件资源中复制默认贴图到数据文件夹
     */
    private static void copyDefaultTextures(JavaPlugin plugin) {
        for (String fileName : textureFileMap.values()) {
            File targetFile = new File(textureDirectory, fileName);

            // 如果文件不存在，从插件资源中复制
            if (!targetFile.exists()) {
                try {
                    // 尝试从插件资源中复制文件
                    java.io.InputStream inputStream = plugin.getResource("textures/" + fileName);
                    if (inputStream != null) {
                        java.nio.file.Files.copy(inputStream, targetFile.toPath());
                        logger.info("从插件资源复制贴图: " + fileName);
                        inputStream.close();
                    } else {
                        logger.warning("插件资源中未找到贴图文件: textures/" + fileName);
                    }
                } catch (Exception e) {
                    logger.warning("复制贴图文件失败: " + fileName + " - " + e.getMessage());
                }
            }
        }
    }

    /**
     * 预加载所有贴图
     */
    private static void preloadAllTextures() {
        if (!textureDirectory.exists()) {
            logger.warning("贴图目录不存在: " + textureDirectory.getAbsolutePath());
            logger.info("创建贴图目录...");
            textureDirectory.mkdirs();
            return;
        }

        int loadedCount = 0;
        int totalCount = textureFileMap.size();

        for (Map.Entry<Material, String> entry : textureFileMap.entrySet()) {
            Material material = entry.getKey();
            String fileName = entry.getValue();

            ImageIcon icon = loadTextureFile(fileName);
            if (icon != null) {
                textureCache.put(material, icon);
                loadedCount++;
                logger.info("成功加载贴图: " + fileName + " -> " + material.name());
            } else {
                logger.warning("无法加载贴图: " + fileName + " -> " + material.name());
            }
        }

        logger.info("贴图加载完成: " + loadedCount + "/" + totalCount);
    }

    /**
     * 加载附魔光效贴图
     */
    private static void loadEnchantmentGlint() {
        try {
            // 调试信息：显示贴图目录路径
            logger.info("正在查找附魔光效贴图，目录: " + textureDirectory.getAbsolutePath());

            // 检查目录中的文件
            File[] files = textureDirectory.listFiles();
            if (files != null) {
                logger.info("贴图目录中的文件数量: " + files.length);
                for (File file : files) {
                    if (file.getName().contains("enchanted") || file.getName().contains("glint")) {
                        logger.info("找到相关文件: " + file.getName());
                    }
                }
            }

            // 加载基础贴图
            enchantmentGlintTexture = loadSpecialTexture("enchanted_item_glint");

            if (enchantmentGlintTexture != null) {
                logger.info("成功加载附魔光效贴图: enchanted_item_glint");

                // 尝试加载动画配置
                loadEnchantmentAnimation();

                // 如果有动画帧，提取帧数据
                if (enchantmentGlintFrames != null) {
                    logger.info("附魔光效动画已启用，共 " + enchantmentGlintFrames.length + " 帧");
                }
            } else {
                logger.info("未找到附魔光效贴图文件，将使用程序生成的光效");
                logger.info("请确保文件名为: enchanted_item_glint.png (或 .jpg/.jpeg/.gif)");
            }
        } catch (Exception e) {
            logger.warning("加载附魔光效贴图失败: " + e.getMessage());
        }
    }

    /**
     * 加载附魔光效动画配置
     */
    private static void loadEnchantmentAnimation() {
        try {
            File mcmetaFile = new File(textureDirectory, "enchanted_item_glint.png.mcmeta");
            if (mcmetaFile.exists()) {
                // 读取 .mcmeta 文件
                String mcmetaContent = new String(java.nio.file.Files.readAllBytes(mcmetaFile.toPath()));
                logger.info("找到附魔光效动画配置: " + mcmetaFile.getName());

                // 简单解析 frametime（这里可以用更完整的JSON解析）
                if (mcmetaContent.contains("\"frametime\"")) {
                    try {
                        String[] parts = mcmetaContent.split("\"frametime\"\\s*:\\s*");
                        if (parts.length > 1) {
                            String frameTimeStr = parts[1].split("[,}]")[0].trim();
                            frameTime = Integer.parseInt(frameTimeStr);
                            logger.info("附魔光效帧时间设置为: " + frameTime + " 游戏刻");
                        }
                    } catch (Exception e) {
                        logger.warning("解析帧时间失败，使用默认值: " + e.getMessage());
                    }
                }

                // 提取动画帧
                extractAnimationFrames();
            } else {
                logger.info("未找到 .mcmeta 配置文件，附魔光效将使用静态显示");
            }
        } catch (Exception e) {
            logger.warning("加载附魔光效动画配置失败: " + e.getMessage());
        }
    }

    /**
     * 从贴图中提取动画帧
     */
    private static void extractAnimationFrames() {
        try {
            if (enchantmentGlintTexture == null)
                return;

            BufferedImage fullImage = new BufferedImage(
                    enchantmentGlintTexture.getIconWidth(),
                    enchantmentGlintTexture.getIconHeight(),
                    BufferedImage.TYPE_INT_ARGB);

            Graphics2D g = fullImage.createGraphics();
            g.drawImage(enchantmentGlintTexture.getImage(), 0, 0, null);
            g.dispose();

            // 检查是否是动画贴图（高度大于宽度表示垂直排列的帧）
            int width = fullImage.getWidth();
            int height = fullImage.getHeight();

            if (height > width) {
                // 计算帧数
                int frameCount = height / width;
                enchantmentGlintFrames = new BufferedImage[frameCount];

                // 提取每一帧
                for (int i = 0; i < frameCount; i++) {
                    enchantmentGlintFrames[i] = fullImage.getSubimage(0, i * width, width, width);
                }

                logger.info("成功提取 " + frameCount + " 个附魔光效动画帧");
            }
        } catch (Exception e) {
            logger.warning("提取附魔光效动画帧失败: " + e.getMessage());
        }
    }

    /**
     * 从文件加载贴图
     */
    private static ImageIcon loadTextureFile(String fileName) {
        try {
            File textureFile = new File(textureDirectory, fileName);
            if (!textureFile.exists()) {
                logger.warning("贴图文件不存在: " + textureFile.getAbsolutePath());
                return null;
            }

            // 加载图片
            BufferedImage originalImage = ImageIO.read(textureFile);
            if (originalImage == null) {
                logger.warning("无法读取贴图文件: " + textureFile.getAbsolutePath());
                return null;
            }

            // 缩放图片到合适的大小 (64x64)
            Image scaledImage = originalImage.getScaledInstance(64, 64, Image.SCALE_SMOOTH);
            return new ImageIcon(scaledImage);

        } catch (Exception e) {
            logger.severe("加载贴图文件时出错: " + fileName + " - " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取装备贴图
     */
    public static ImageIcon getTexture(Material material) {
        if (!initialized) {
            logger.warning("贴图管理器未初始化！");
            return null;
        }

        return textureCache.get(material);
    }

    /**
     * 获取物品贴图（支持数据值）
     *
     * @param material 物品材质
     * @param data     物品数据值
     * @return 物品贴图图标
     */
    public static ImageIcon getTexture(Material material, short data) {
        if (!initialized) {
            logger.warning("贴图管理器未初始化！");
            return null;
        }

        // 特殊处理药水类型
        if (material == Material.POTION) {
            return getPotionTexture(data);
        }

        return getTexture(material);
    }

    /**
     * 根据药水数据值获取对应的贴图
     */
    private static ImageIcon getPotionTexture(short data) {
        // 在1.8中，喷溅药水的数据值通常是16384 + 药水效果ID
        // 16384 = 0x4000，表示喷溅药水的标志位
        boolean isSplash = (data & 16384) != 0;

        if (isSplash) {
            // 尝试获取喷溅药水贴图
            ImageIcon splashTexture = loadSpecialTexture("splash_potion");
            if (splashTexture != null) {
                return splashTexture;
            }
        }

        // 如果没有找到特殊贴图，返回普通药水贴图
        return textureCache.get(Material.POTION);
    }

    /**
     * 加载特殊贴图文件
     */
    private static ImageIcon loadSpecialTexture(String fileName) {
        try {
            File textureFile = new File(textureDirectory, fileName + ".png");
            if (!textureFile.exists()) {
                // 尝试其他格式
                String[] extensions = { ".jpg", ".jpeg", ".gif" };
                for (String ext : extensions) {
                    textureFile = new File(textureDirectory, fileName + ext);
                    if (textureFile.exists()) {
                        break;
                    }
                }
                if (!textureFile.exists()) {
                    return null;
                }
            }

            BufferedImage originalImage = ImageIO.read(textureFile);
            if (originalImage == null) {
                return null;
            }

            Image scaledImage = originalImage.getScaledInstance(64, 64, Image.SCALE_SMOOTH);
            return new ImageIcon(scaledImage);

        } catch (Exception e) {
            logger.warning("加载特殊贴图失败: " + fileName + " - " + e.getMessage());
            return null;
        }
    }

    /**
     * 获取附魔装备贴图
     *
     * @param material    装备材质
     * @param isEnchanted 是否附魔
     * @return 装备贴图图标
     */
    public static ImageIcon getEnchantedTexture(Material material, boolean isEnchanted) {
        if (!initialized) {
            logger.warning("贴图管理器未初始化！");
            return null;
        }

        if (!isEnchanted) {
            return getTexture(material);
        }

        // 尝试获取专用的附魔贴图（通过缓存键查找）
        String enchantedKey = material.name() + "_ENCHANTED";
        for (Map.Entry<Material, ImageIcon> entry : textureCache.entrySet()) {
            if (entry.getKey().name().equals(enchantedKey)) {
                return entry.getValue();
            }
        }

        // 如果没有专用附魔贴图，创建带光效的贴图
        return createEnchantedTexture(material);
    }

    /**
     * 创建带附魔光效的贴图
     */
    private static ImageIcon createEnchantedTexture(Material material) {
        ImageIcon baseTexture = getTexture(material);
        if (baseTexture == null) {
            return null;
        }

        try {
            // 获取基础贴图
            BufferedImage baseImage = new BufferedImage(64, 64, BufferedImage.TYPE_INT_ARGB);
            Graphics2D g2d = baseImage.createGraphics();
            g2d.drawImage(baseTexture.getImage(), 0, 0, null);

            // 添加附魔光效
            addEnchantmentGlint(g2d);

            g2d.dispose();
            return new ImageIcon(baseImage);

        } catch (Exception e) {
            logger.warning("创建附魔贴图失败: " + material.name() + " - " + e.getMessage());
            return baseTexture; // 返回原始贴图
        }
    }

    /**
     * 添加附魔光效
     */
    private static void addEnchantmentGlint(Graphics2D g2d) {
        if (enchantmentGlintTexture != null) {
            // 使用真实的附魔光效贴图
            BufferedImage currentFrame = getCurrentEnchantmentFrame();

            if (currentFrame != null) {
                // 使用游戏中的混合模式
                g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.5f));

                // 绘制当前帧，使用游戏中的渲染方式
                g2d.drawImage(currentFrame, 0, 0, 64, 64, null);

                // 添加额外的光泽层
                g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.3f));
                g2d.drawImage(currentFrame, -1, -1, 66, 66, null);
            } else {
                // 如果没有动画帧，使用静态贴图
                g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.4f));
                g2d.drawImage(enchantmentGlintTexture.getImage(), 0, 0, 64, 64, null);
            }
        } else {
            // 使用程序生成的光效，模拟游戏中的附魔光泽
            createGameLikeEnchantmentEffect(g2d);
        }
    }

    /**
     * 获取当前附魔光效动画帧
     */
    private static BufferedImage getCurrentEnchantmentFrame() {
        if (enchantmentGlintFrames == null || enchantmentGlintFrames.length == 0) {
            return null;
        }

        // 计算当前应该显示的帧
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastFrameTime > frameTime * 50) { // 50ms ≈ 1游戏刻
            currentGlintFrame = (currentGlintFrame + 1) % enchantmentGlintFrames.length;
            lastFrameTime = currentTime;
        }

        return enchantmentGlintFrames[currentGlintFrame];
    }

    /**
     * 创建类似游戏的附魔光效
     */
    private static void createGameLikeEnchantmentEffect(Graphics2D g2d) {
        // 保存原始的合成模式
        Composite originalComposite = g2d.getComposite();

        // 第一层：紫色光泽
        g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.3f));
        GradientPaint purpleGradient = new GradientPaint(
                0, 0, new Color(138, 43, 226, 80), // 蓝紫色
                64, 64, new Color(75, 0, 130, 80) // 靛青色
        );
        g2d.setPaint(purpleGradient);
        g2d.fillRect(0, 0, 64, 64);

        // 第二层：动态条纹效果
        g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.4f));
        g2d.setStroke(new BasicStroke(1.5f, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND));

        // 绘制斜向光泽条纹
        for (int i = -32; i < 96; i += 6) {
            // 交替使用不同的颜色
            if (i % 12 == 0) {
                g2d.setColor(new Color(255, 255, 255, 120)); // 白色高光
            } else {
                g2d.setColor(new Color(200, 150, 255, 100)); // 淡紫色
            }
            g2d.drawLine(i, 0, i + 32, 64);
        }

        // 第三层：边缘高光
        g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.6f));
        g2d.setStroke(new BasicStroke(1.0f));
        g2d.setColor(new Color(255, 255, 255, 180));
        g2d.drawRoundRect(1, 1, 62, 62, 6, 6);

        // 第四层：内部光晕
        g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.2f));
        RadialGradientPaint radialGradient = new RadialGradientPaint(
                32, 32, 20,
                new float[] { 0.0f, 1.0f },
                new Color[] { new Color(255, 255, 255, 100), new Color(255, 255, 255, 0) });
        g2d.setPaint(radialGradient);
        g2d.fillOval(12, 12, 40, 40);

        // 恢复原始合成模式
        g2d.setComposite(originalComposite);
    }

    /**
     * 检查是否有指定材质的贴图
     */
    public static boolean hasTexture(Material material) {
        return initialized && textureCache.containsKey(material);
    }

    /**
     * 重新加载所有贴图
     */
    public static void reloadTextures() {
        if (!initialized) {
            logger.warning("贴图管理器未初始化，无法重新加载！");
            return;
        }

        logger.info("重新加载装备贴图...");
        textureCache.clear();
        preloadAllTextures();
        logger.info("装备贴图重新加载完成！");
    }

    /**
     * 获取贴图统计信息
     */
    public static String getStatistics() {
        if (!initialized) {
            return "贴图管理器未初始化";
        }

        int totalMappings = textureFileMap.size();
        int loadedTextures = textureCache.size();

        return String.format("贴图统计: %d/%d 已加载", loadedTextures, totalMappings);
    }

    /**
     * 清理资源
     */
    public static void cleanup() {
        if (initialized) {
            textureCache.clear();
            textureFileMap.clear();
            initialized = false;
            logger.info("装备贴图管理器已清理");
        }
    }
}
