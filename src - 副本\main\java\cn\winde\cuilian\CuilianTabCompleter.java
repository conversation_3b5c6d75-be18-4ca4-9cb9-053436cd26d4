package cn.winde.cuilian;

import cn.winde.cuilian.suit.SuitManager;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Tab补全器，为/cuilian命令提供参数补全功能
 */
public class CuilianTabCompleter implements TabCompleter {

    // 玩家可用的命令
    private static final List<String> PLAYER_ARGS = Arrays.asList(
            "effect", "suit", "gui");

    // 管理员命令
    private static final List<String> ADMIN_ARGS = Arrays.asList(
            "set", "setall", "reload", "opensetui", "name", "stone", "rod");

    // 淬炼石类型 - 整理为更清晰的格式
    private static final List<String> STONE_TYPES = Arrays.asList(
            "普通", "中等", "高等", "上等", "符咒", "吞噬");

    // 数量选项 - 常用数量
    private static final List<String> AMOUNTS = Arrays.asList(
            "1", "5", "10", "16", "32", "64");

    // 玩家可用的effect参数
    private static final List<String> PLAYER_EFFECT_ARGS = Arrays.asList(
            "on", "off", "status");

    // 管理员effect参数
    private static final List<String> ADMIN_EFFECT_ARGS = Arrays.asList(
            "6", "9", "12", "15", "18", "19", "type");

    private static final List<String> EFFECT_TYPES = Arrays.asList(
            "wings", "halo", "flame", "nebula", "tornado", "stars");

    /**
     * 动态获取星级列表，基于配置文件中的最大星级
     * @return 星级列表
     */
    private List<String> getStarLevels() {
        List<String> levels = new ArrayList<>();
        int maxLevel = Cuilian.getMaxConfiguredLevel();

        for (int i = 1; i <= maxLevel; i++) {
            levels.add(String.valueOf(i));
        }

        return levels;
    }

    // 玩家可用的suit参数
    private static final List<String> PLAYER_SUIT_ARGS = Arrays.asList(
            "toggle");

    // 管理员suit参数
    private static final List<String> ADMIN_SUIT_ARGS = Arrays.asList(
            "list", "info", "check", "give", "space");

    // GUI类型参数
    private static final List<String> GUI_TYPES = Arrays.asList(
            "equipment", "eq", "suit", "preview", "effect", "fx", "enhance", "info", "help");

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        // 根据权限返回不同的补全选项

        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            // 第一个参数的补全，根据权限返回不同选项
            List<String> availableCommands = new ArrayList<>();

            // 所有玩家都可以使用的命令
            availableCommands.addAll(PLAYER_ARGS);

            // 如果是OP，添加管理员命令
            if (sender.isOp()) {
                availableCommands.addAll(ADMIN_ARGS);
            }

            return filterCompletions(availableCommands, args[0]);
        } else if (args.length == 2) {
            // 第二个参数的补全，根据第一个参数的不同提供不同的补全选项
            switch (args[0].toLowerCase()) {
                case "set":
                case "setall":
                    return filterCompletions(getStarLevels(), args[1]);
                case "effect":
                    // effect命令补全，根据权限返回不同选项
                    List<String> effectOptions = new ArrayList<>();
                    effectOptions.addAll(PLAYER_EFFECT_ARGS);
                    if (sender.isOp()) {
                        effectOptions.addAll(ADMIN_EFFECT_ARGS);
                    }
                    return filterCompletions(effectOptions, args[1]);
                case "suit":
                    // suit命令补全，根据权限返回不同选项
                    List<String> suitOptions = new ArrayList<>();
                    suitOptions.addAll(PLAYER_SUIT_ARGS);
                    if (sender.isOp()) {
                        suitOptions.addAll(ADMIN_SUIT_ARGS);
                    }
                    return filterCompletions(suitOptions, args[1]);
                case "stone":
                    // stone命令的第二个参数：淬炼石类型
                    return filterCompletions(STONE_TYPES, args[1]);
                case "rod":
                    // rod命令的第二个参数：等级
                    return filterCompletions(getStarLevels(), args[1]);
                case "gui":
                    // gui命令的第二个参数：GUI类型
                    return filterCompletions(GUI_TYPES, args[1]);
            }
        } else if (args.length == 3) {
            // 第三个参数的补全
            if (args[0].equalsIgnoreCase("stone") && sender.isOp()) {
                // stone命令的第三个参数：数量
                return filterCompletions(AMOUNTS, args[2]);
            } else if (args[0].equalsIgnoreCase("rod") && sender.isOp()) {
                // rod命令的第三个参数：数量
                return filterCompletions(AMOUNTS, args[2]);
            } else if (args[0].equalsIgnoreCase("effect") && sender.isOp()) {
                // 如果是effect命令 (只有管理员可以使用高级选项)
                if (args[1].matches("^(6|9|12|15|18|19)$") || args[1].equalsIgnoreCase("type")) {
                    // 如果第二个参数是等级数字或者是type，提供特效类型选项
                    return filterCompletions(EFFECT_TYPES, args[2]);
                }
            } else if (args[0].equalsIgnoreCase("suit") && sender.isOp()) {
                // 如果是suit命令 (只有管理员可以使用需要第三个参数的命令)
                if (args[1].equalsIgnoreCase("info")) {
                    // 管理员可以查看套装信息
                    return filterCompletions(getSuitNames(), args[2]);
                } else if (args[1].equalsIgnoreCase("give")) {
                    // 管理员可以给予套装
                    return filterCompletions(getSuitNames(), args[2]);
                } else if (args[1].equalsIgnoreCase("space")) {
                    // 管理员可以检查玩家背包空间
                    return filterCompletions(getOnlinePlayerNames(), args[2]);
                }
            }
        } else if (args.length == 4) {
            // 第四个参数的补全
            if (args[0].equalsIgnoreCase("stone") && sender.isOp()) {
                // stone命令的第四个参数：玩家名
                return filterCompletions(getOnlinePlayerNames(), args[3]);
            } else if (args[0].equalsIgnoreCase("rod") && sender.isOp()) {
                // rod命令的第四个参数：玩家名
                return filterCompletions(getOnlinePlayerNames(), args[3]);
            } else if (args[0].equalsIgnoreCase("suit") && args[1].equalsIgnoreCase("give") && sender.isOp()) {
                // suit give <套装名> <玩家名> (只有管理员可以使用)
                return filterCompletions(getOnlinePlayerNames(), args[3]);
            }
        }

        return completions;
    }

    /**
     * 根据已输入的前缀过滤补全列表
     *
     * @param completions 可能的补全列表
     * @param prefix      已输入的前缀
     * @return 过滤后的补全列表
     */
    private List<String> filterCompletions(List<String> completions, String prefix) {
        return completions.stream()
                .filter(s -> s.toLowerCase().startsWith(prefix.toLowerCase()))
                .collect(Collectors.toList());
    }

    /**
     * 获取所有在线玩家的名称
     *
     * @return 在线玩家名称列表
     */
    private List<String> getOnlinePlayerNames() {
        List<String> playerNames = new ArrayList<>();
        for (Player player : Bukkit.getOnlinePlayers()) {
            playerNames.add(player.getName());
        }
        return playerNames;
    }

    /**
     * 获取所有套装名称
     *
     * @return 套装名称列表
     */
    private List<String> getSuitNames() {
        try {
            return new ArrayList<>(SuitManager.getAllSuits());
        } catch (Exception e) {
            return Arrays.asList("神武套装", "圣光套装", "烈焰套装", "星辰套装", "暗影套装", "新手套装");
        }
    }
}
