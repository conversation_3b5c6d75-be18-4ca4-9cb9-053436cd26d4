#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除1.8.8版本中未使用的贴图文件
基于对Minecraft版本历史的分析
"""

import os
from pathlib import Path

def delete_unused_textures():
    """删除1.8.8版本中不支持的贴图文件"""
    texture_dir = Path("src/main/resources/textures")
    
    if not texture_dir.exists():
        print(f"贴图目录不存在: {texture_dir}")
        return
    
    # 1.8.8版本不支持的文件列表
    files_to_delete = [
        # 1.9+引入的物品
        "shield.png",
        "elytra.png", 
        "broken_elytra.png",
        "end_crystal.png",
        "totem_of_undying.png",
        "spectral_arrow.png",
        "tipped_arrow_base.png",
        "tipped_arrow_head.png",
        "lingering_potion.png",
        "splash_potion.png",
        "dragon_breath.png",
        "chorus_fruit.png",
        "popped_chorus_fruit.png",
        "beetroot.png",
        "beetroot_seeds.png", 
        "beetroot_soup.png",
        
        # 1.10+
        "structure_void.png",
        
        # 1.11+
        "shulker_shell.png",
        "iron_nugget.png",
        
        # 1.12+
        "knowledge_book.png",
        
        # 1.13+
        "turtle_helmet.png",
        "turtle_egg.png",
        "heart_of_the_sea.png",
        "nautilus_shell.png",
        "phantom_membrane.png",
        "trident.png",
        "kelp.png",
        "dried_kelp.png",
        "seagrass.png",
        "sea_pickle.png",
        "cod.png",
        "salmon.png",
        "tropical_fish.png",
        "pufferfish.png",
        "cod_bucket.png",
        "salmon_bucket.png",
        "tropical_fish_bucket.png",
        "pufferfish_bucket.png",
        "scute.png",
        
        # 1.14+
        "crossbow_standby.png",
        "crossbow_arrow.png", 
        "crossbow_firework.png",
        "crossbow_pulling_0.png",
        "crossbow_pulling_1.png",
        "crossbow_pulling_2.png",
        "suspicious_stew.png",
        "sweet_berries.png",
        "honeycomb.png",
        "honey_bottle.png",
        "campfire.png",
        "soul_campfire.png",
        "lantern.png",
        "soul_lantern.png",
        "bell.png",
        "bamboo.png",
        
        # 1.16+
        "netherite_helmet.png",
        "netherite_chestplate.png",
        "netherite_leggings.png",
        "netherite_boots.png",
        "netherite_sword.png",
        "netherite_pickaxe.png",
        "netherite_axe.png",
        "netherite_shovel.png",
        "netherite_hoe.png",
        "netherite_ingot.png",
        "netherite_scrap.png",
        "chain.png",
        "warped_fungus_on_a_stick.png",
        "nether_sprouts.png",
        "crimson_door.png",
        "warped_door.png",
        "crimson_sign.png",
        "warped_sign.png",
        
        # 各种新版本的门、告示牌、船等 (1.9+)
        "acacia_boat.png",
        "acacia_door.png",
        "acacia_sign.png",
        "birch_boat.png",
        "birch_door.png",
        "birch_sign.png",
        "dark_oak_boat.png",
        "dark_oak_door.png",
        "dark_oak_sign.png",
        "jungle_boat.png",
        "jungle_door.png",
        "jungle_sign.png",
        "spruce_boat.png",
        "spruce_door.png",
        "spruce_sign.png",
        "oak_boat.png",
        "oak_door.png",
        "oak_sign.png",
        
        # 各种染料（1.14+重命名，1.8.8中是INK_SACK的数据值）
        "black_dye.png",
        "blue_dye.png",
        "brown_dye.png",
        "cyan_dye.png",
        "gray_dye.png",
        "green_dye.png",
        "light_blue_dye.png",
        "light_gray_dye.png",
        "lime_dye.png",
        "magenta_dye.png",
        "orange_dye.png",
        "pink_dye.png",
        "purple_dye.png",
        "red_dye.png",
        "white_dye.png",
        "yellow_dye.png",
        
        # 各种旗帜图案 (1.8+但在1.8.8中可能不完全支持)
        "creeper_banner_pattern.png",
        "flower_banner_pattern.png",
        "globe_banner_pattern.png",
        "mojang_banner_pattern.png",
        "piglin_banner_pattern.png",
        "skull_banner_pattern.png",
        
        # 其他新版本物品
        "armor_stand.png",  # 1.8中存在但作为实体，不是物品
        "barrier.png",      # 1.8中存在但通常不作为物品使用
        "command_block_minecart.png",
        "comparator.png",   # 1.8中是REDSTONE_COMPARATOR
        "repeater.png",     # 1.8中是DIODE
        "hopper.png",       # 1.8中存在但可能命名不同
        "cauldron.png",     # 1.8中存在但可能命名不同
        "brewing_stand.png", # 1.8中是BREWING_STAND_ITEM
        "empty_armor_slot_boots.png",
        "empty_armor_slot_chestplate.png",
        "empty_armor_slot_helmet.png",
        "empty_armor_slot_leggings.png",
        "empty_armor_slot_shield.png",
        "filled_map_markings.png",
        "firework_rocket.png",
        "firework_star.png",
        "firework_star_overlay.png",
        "fishing_rod_cast.png",
        "bow_pulling_0.png",  # 1.8中弓的拉弓动画可能不支持
        "bow_pulling_1.png",
        "bow_pulling_2.png",
        "potion_overlay.png",
        "spawn_egg.png",
        "spawn_egg_overlay.png",
        "leather_boots_overlay.png",
        "leather_chestplate_overlay.png",
        "leather_helmet_overlay.png",
        "leather_leggings_overlay.png",
        "leather_horse_armor.png",  # 1.8中可能不支持
        "iron_horse_armor.png",     # 1.8中可能不支持
        "golden_horse_armor.png",   # 1.8中可能不支持
        "diamond_horse_armor.png",  # 1.8中可能不支持
        "lead.png",                 # 1.8中不存在
        "mutton.png",               # 1.8中不存在
        "cooked_mutton.png",        # 1.8中不存在
        "rabbit.png",               # 1.8中不存在
        "cooked_rabbit.png",        # 1.8中不存在
        "rabbit_foot.png",          # 1.8中不存在
        "rabbit_hide.png",          # 1.8中不存在
        "rabbit_stew.png",          # 1.8中不存在
        "poisonous_potato.png",     # 1.8中可能不存在
        
        # 音乐唱片（某些可能是新版本的）
        "music_disc_pigstep.png",   # 1.16+
        
        # 其他可能不支持的文件
        "ruby.png",  # 从未在正式版本中存在
        "bone_meal.png",  # 1.8中可能是INK_SACK的数据值
        "cocoa_beans.png", # 1.8中可能是INK_SACK的数据值
    ]
    
    deleted_count = 0
    total_size = 0
    
    print("开始删除1.8.8版本中不支持的贴图文件...")
    print("=" * 60)
    
    for filename in files_to_delete:
        file_path = texture_dir / filename
        if file_path.exists():
            try:
                size = file_path.stat().st_size
                total_size += size
                file_path.unlink()
                deleted_count += 1
                print(f"✓ 删除: {filename} ({size} bytes)")
            except Exception as e:
                print(f"✗ 删除失败: {filename} - {e}")
        else:
            print(f"- 文件不存在: {filename}")
    
    print("=" * 60)
    print(f"删除完成!")
    print(f"删除了 {deleted_count} 个文件")
    print(f"节省空间: {total_size} bytes ({total_size/1024:.1f} KB)")
    
    # 显示剩余的重要文件
    remaining_files = list(texture_dir.glob("*.png"))
    print(f"\n剩余PNG文件: {len(remaining_files)} 个")
    
    if len(remaining_files) < 50:  # 如果文件不多，显示列表
        print("\n剩余的重要文件:")
        for file in sorted(remaining_files):
            print(f"  - {file.name}")

if __name__ == "__main__":
    print("1.8.8版本贴图清理工具")
    print("此脚本将删除1.8.8版本中不支持的贴图文件")
    print("这将显著减小插件的大小")
    print()
    
    response = input("确定要继续吗? (y/N): ").lower().strip()
    if response in ['y', 'yes', '是']:
        delete_unused_textures()
    else:
        print("操作已取消")
