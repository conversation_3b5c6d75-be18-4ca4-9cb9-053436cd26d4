package cn.winde.cuilian.suit;

import cn.winde.cuilian.Cuilian;
import org.bukkit.Bukkit;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.event.inventory.InventoryOpenEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerItemHeldEvent;
import org.bukkit.event.player.PlayerDropItemEvent;
import org.bukkit.event.player.PlayerPickupItemEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.ArrayList;
import java.util.List;

/**
 * 套装显示更新器
 * 负责实时更新玩家背包中套装物品的激活状态显示
 */
public class SuitDisplayUpdater implements Listener {

    /**
     * 玩家加入游戏时启动定时更新任务
     */
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();

        // 延迟3秒后开始更新，给玩家时间加载
        Bukkit.getScheduler().runTaskLater(Cuilian.getInstance(), () -> {
            startPeriodicUpdate(player);
        }, 60L); // 3秒
    }

    /**
     * 玩家打开背包时更新套装显示
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onInventoryOpen(InventoryOpenEvent event) {
        if (event.getPlayer() instanceof Player) {
            Player player = (Player) event.getPlayer();

            // 延迟1个tick更新，确保背包已经打开
            Bukkit.getScheduler().runTask(Cuilian.getInstance(), () -> {
                updatePlayerSuitItems(player);
            });
        }
    }

    /**
     * 玩家点击背包时更新套装显示
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onInventoryClick(InventoryClickEvent event) {
        if (event.getWhoClicked() instanceof Player) {
            Player player = (Player) event.getWhoClicked();

            // 暂时禁用频繁的套装更新，避免重置装备等级
            /*
             * // 立即更新一次
             * Bukkit.getScheduler().runTask(Cuilian.getInstance(), () -> {
             * updatePlayerSuitItems(player);
             * });
             *
             * // 延迟1个tick再更新一次，确保物品已经移动
             * Bukkit.getScheduler().runTaskLater(Cuilian.getInstance(), () -> {
             * updatePlayerSuitItems(player);
             * }, 1L);
             *
             * // 延迟3个tick最后更新一次，以防万一
             * Bukkit.getScheduler().runTaskLater(Cuilian.getInstance(), () -> {
             * updatePlayerSuitItems(player);
             * }, 3L);
             */
        }
    }

    /**
     * 玩家关闭背包时更新套装显示
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onInventoryClose(InventoryCloseEvent event) {
        if (event.getPlayer() instanceof Player) {
            Player player = (Player) event.getPlayer();

            // 立即更新
            Bukkit.getScheduler().runTask(Cuilian.getInstance(), () -> {
                updatePlayerSuitItems(player);
            });
        }
    }

    /**
     * 玩家拖拽物品时更新套装显示
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onInventoryDrag(InventoryDragEvent event) {
        if (event.getWhoClicked() instanceof Player) {
            Player player = (Player) event.getWhoClicked();

            // 延迟更新，确保拖拽完成
            Bukkit.getScheduler().runTaskLater(Cuilian.getInstance(), () -> {
                updatePlayerSuitItems(player);
            }, 2L);
        }
    }

    /**
     * 玩家切换手持物品时更新套装显示
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerItemHeld(PlayerItemHeldEvent event) {
        Player player = event.getPlayer();

        // 延迟更新
        Bukkit.getScheduler().runTaskLater(Cuilian.getInstance(), () -> {
            updatePlayerSuitItems(player);
        }, 1L);
    }

    /**
     * 玩家丢弃物品时更新套装显示
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerDropItem(PlayerDropItemEvent event) {
        Player player = event.getPlayer();

        // 延迟更新
        Bukkit.getScheduler().runTaskLater(Cuilian.getInstance(), () -> {
            updatePlayerSuitItems(player);
        }, 1L);
    }

    /**
     * 玩家拾取物品时更新套装显示
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerPickupItem(PlayerPickupItemEvent event) {
        Player player = event.getPlayer();

        // 延迟更新
        Bukkit.getScheduler().runTaskLater(Cuilian.getInstance(), () -> {
            updatePlayerSuitItems(player);
        }, 1L);
    }

    /**
     * 启动玩家的定时更新任务和装备监控
     *
     * @param player 玩家
     */
    private void startPeriodicUpdate(Player player) {
        // 启动定时更新任务
        new BukkitRunnable() {
            @Override
            public void run() {
                // 如果玩家离线，取消任务
                if (!player.isOnline()) {
                    this.cancel();
                    return;
                }

                // 更新玩家的套装物品显示
                updatePlayerSuitItems(player);
            }
        }.runTaskTimer(Cuilian.getInstance(), 0L, 20L); // 每1秒更新一次

        // 启动装备变更监控任务（更频繁的检查）
        new BukkitRunnable() {
            private ItemStack lastHelmet = null;
            private ItemStack lastChestplate = null;
            private ItemStack lastLeggings = null;
            private ItemStack lastBoots = null;

            @Override
            public void run() {
                // 如果玩家离线，取消任务
                if (!player.isOnline()) {
                    this.cancel();
                    return;
                }

                // 检查装备是否发生变化
                ItemStack currentHelmet = player.getInventory().getHelmet();
                ItemStack currentChestplate = player.getInventory().getChestplate();
                ItemStack currentLeggings = player.getInventory().getLeggings();
                ItemStack currentBoots = player.getInventory().getBoots();

                boolean equipmentChanged = false;

                if (!isSameItem(lastHelmet, currentHelmet)) {
                    equipmentChanged = true;
                    lastHelmet = currentHelmet != null ? currentHelmet.clone() : null;
                }

                if (!isSameItem(lastChestplate, currentChestplate)) {
                    equipmentChanged = true;
                    lastChestplate = currentChestplate != null ? currentChestplate.clone() : null;
                }

                if (!isSameItem(lastLeggings, currentLeggings)) {
                    equipmentChanged = true;
                    lastLeggings = currentLeggings != null ? currentLeggings.clone() : null;
                }

                if (!isSameItem(lastBoots, currentBoots)) {
                    equipmentChanged = true;
                    lastBoots = currentBoots != null ? currentBoots.clone() : null;
                }

                // 如果装备发生变化，立即更新显示
                if (equipmentChanged) {
                    updatePlayerSuitItems(player);
                }
            }
        }.runTaskTimer(Cuilian.getInstance(), 0L, 5L); // 每0.25秒检查一次装备变更
    }

    /**
     * 检查两个物品是否相同
     *
     * @param item1 物品1
     * @param item2 物品2
     * @return 是否相同
     */
    private boolean isSameItem(ItemStack item1, ItemStack item2) {
        if (item1 == null && item2 == null) {
            return true;
        }
        if (item1 == null || item2 == null) {
            return false;
        }
        return item1.equals(item2);
    }

    /**
     * 更新玩家背包和装备栏中的套装物品显示（安全方式，只更新lore）
     *
     * @param player 玩家
     */
    private void updatePlayerSuitItems(Player player) {
        try {
            // 更新背包中的套装物品
            updateInventorySuitItems(player);

            // 更新装备栏中的套装物品
            updateEquippedSuitItems(player);

        } catch (Exception e) {
            // 静默处理异常，避免影响游戏体验
        }
    }

    /**
     * 更新背包中的套装物品（只更新lore，不替换物品）
     *
     * @param player 玩家
     */
    private void updateInventorySuitItems(Player player) {
        for (int i = 0; i < 36; i++) { // 只检查背包格子，不包括装备栏
            ItemStack item = player.getInventory().getItem(i);
            if (item != null && isSuitItem(item)) {
                updateSuitItemLore(item, player);
            }
        }
    }

    /**
     * 更新装备栏中的套装物品（只更新lore，不替换物品）
     *
     * @param player 玩家
     */
    private void updateEquippedSuitItems(Player player) {
        // 更新头盔
        ItemStack helmet = player.getInventory().getHelmet();
        if (helmet != null && isSuitItem(helmet)) {
            updateSuitItemLore(helmet, player);
        }

        // 更新胸甲
        ItemStack chestplate = player.getInventory().getChestplate();
        if (chestplate != null && isSuitItem(chestplate)) {
            updateSuitItemLore(chestplate, player);
        }

        // 更新护腿
        ItemStack leggings = player.getInventory().getLeggings();
        if (leggings != null && isSuitItem(leggings)) {
            updateSuitItemLore(leggings, player);
        }

        // 更新靴子
        ItemStack boots = player.getInventory().getBoots();
        if (boots != null && isSuitItem(boots)) {
            updateSuitItemLore(boots, player);
        }
    }

    /**
     * 检查物品是否是套装物品
     *
     * @param item 物品
     * @return 是否是套装物品
     */
    private boolean isSuitItem(ItemStack item) {
        return item != null && item.hasItemMeta() && item.getItemMeta().hasLore() &&
                getSuitNameFromItem(item) != null;
    }

    /**
     * 安全地更新套装物品的lore（不替换整个物品）
     *
     * @param item   要更新的物品
     * @param player 玩家
     */
    private void updateSuitItemLore(ItemStack item, Player player) {
        try {
            String suitName = getSuitNameFromItem(item);
            if (suitName == null)
                return;

            ItemMeta meta = item.getItemMeta();
            if (meta == null || !meta.hasLore())
                return;

            List<String> lore = new ArrayList<>(meta.getLore());
            boolean isActivated = suitName.equals(SuitManager.getPlayerSuit(player.getName()));

            // 更新套装效果相关的lore行，包括淬炼属性合并
            updateSuitEffectLoreWithCuilian(lore, suitName, isActivated, player);

            // 应用更新后的lore
            meta.setLore(lore);
            item.setItemMeta(meta);

        } catch (Exception e) {
            // 静默处理异常
        }
    }

    /**
     * 更新套装效果相关的lore行
     *
     * @param lore        lore列表
     * @param suitName    套装名称
     * @param isActivated 是否激活
     */
    private void updateSuitEffectLore(List<String> lore, String suitName, boolean isActivated) {
        // 从配置文件读取颜色设置
        DisplayConfig config = getDisplayConfig(suitName, isActivated);

        for (int i = 0; i < lore.size(); i++) {
            String line = lore.get(i);

            // 更新套装效果状态行
            if (line.contains("套装效果:") && (line.contains("✓ 已激活") || line.contains("✗ 未激活"))) {
                lore.set(i, config.statusText);
            }
            // 更新属性行（以"- "开头的行，根据属性类型使用不同颜色）
            else if (line.startsWith("§a- ") || line.startsWith("§7- ") || line.startsWith("§c- ") ||
                    line.startsWith("§9- ") || line.startsWith("§f- ") || line.startsWith("§e- ") ||
                    line.startsWith("§d- ") || line.startsWith("§b- ")) {
                String cleanLine = line.replaceAll("^§[a-f0-9]", "");
                String newColor = getAttributeColor(cleanLine, config);
                lore.set(i, newColor + cleanLine);
            }
            // 更新提示信息行
            else if (line.contains("✓ 套装效果已激活") || line.contains("※ 需要穿戴完整套装")) {
                lore.set(i, config.tipText);
            }
        }
    }

    /**
     * 根据属性类型获取对应的颜色
     *
     * @param attributeLine 属性行文本
     * @param config        显示配置
     * @return 对应的颜色代码
     */
    private String getAttributeColor(String attributeLine, DisplayConfig config) {
        if (attributeLine.contains("攻击伤害")) {
            return config.attackDamageColor;
        } else if (attributeLine.contains("防御力")) {
            return config.defenseColor;
        } else if (attributeLine.contains("生命值")) {
            return config.healthColor;
        } else if (attributeLine.contains("移动速度")) {
            return config.speedColor;
        } else if (attributeLine.contains("跳跃高度")) {
            return config.jumpColor;
        } else if (attributeLine.contains("吸血")) {
            return config.vampireColor;
        } else if (attributeLine.contains("特效")) {
            return config.effectColor;
        }
        return config.effectColor; // 默认颜色
    }

    /**
     * 从配置文件获取显示配置
     *
     * @param suitName    套装名称
     * @param isActivated 是否激活
     * @return 显示配置
     */
    private DisplayConfig getDisplayConfig(String suitName, boolean isActivated) {
        String section = isActivated ? "activated" : "deactivated";
        String basePath = "suit." + suitName + ".display." + section;

        // 获取每种属性的颜色配置，如果套装没有配置则使用默认值
        String attackDamageColor = Cuilian.Suit.getString(basePath + ".attack_damage_color", isActivated ? "§c" : "§7");
        String defenseColor = Cuilian.Suit.getString(basePath + ".defense_color", isActivated ? "§9" : "§7");
        String healthColor = Cuilian.Suit.getString(basePath + ".health_color", isActivated ? "§a" : "§7");
        String speedColor = Cuilian.Suit.getString(basePath + ".speed_color", isActivated ? "§f" : "§7");
        String jumpColor = Cuilian.Suit.getString(basePath + ".jump_color", isActivated ? "§e" : "§7");
        String vampireColor = Cuilian.Suit.getString(basePath + ".vampire_color", isActivated ? "§d" : "§7");
        String effectColor = Cuilian.Suit.getString(basePath + ".effect_color", isActivated ? "§b" : "§7");

        String statusText = Cuilian.Suit.getString(basePath + ".status_text",
                isActivated ? "§a§l套装效果: §2§l✓ 已激活" : "§a§l套装效果: §7§l✗ 未激活");
        String tipText = Cuilian.Suit.getString(basePath + ".tip_text",
                isActivated ? "§2§l✓ 套装效果已激活！" : "§c§l※ 需要穿戴完整套装才能激活效果");

        return new DisplayConfig(attackDamageColor, defenseColor, healthColor, speedColor,
                jumpColor, vampireColor, effectColor, statusText, tipText);
    }

    /**
     * 显示配置类
     */
    private static class DisplayConfig {
        public final String attackDamageColor;
        public final String defenseColor;
        public final String healthColor;
        public final String speedColor;
        public final String jumpColor;
        public final String vampireColor;
        public final String effectColor;
        public final String statusText;
        public final String tipText;

        public DisplayConfig(String attackDamageColor, String defenseColor, String healthColor,
                String speedColor, String jumpColor, String vampireColor, String effectColor,
                String statusText, String tipText) {
            this.attackDamageColor = attackDamageColor;
            this.defenseColor = defenseColor;
            this.healthColor = healthColor;
            this.speedColor = speedColor;
            this.jumpColor = jumpColor;
            this.vampireColor = vampireColor;
            this.effectColor = effectColor;
            this.statusText = statusText;
            this.tipText = tipText;
        }
    }

    /**
     * 更新套装效果相关的lore行，包括淬炼属性合并
     *
     * @param lore        lore列表
     * @param suitName    套装名称
     * @param isActivated 是否激活
     * @param player      玩家
     */
    private void updateSuitEffectLoreWithCuilian(List<String> lore, String suitName, boolean isActivated,
            Player player) {
        // 从配置文件读取颜色设置
        DisplayConfig config = getDisplayConfig(suitName, isActivated);

        // 获取玩家的淬炼等级和属性加成
        int cuilianLevel = 0;
        int cuilianAttack = 0;
        int cuilianDefense = 0;
        int cuilianVampire = 0;
        int cuilianJump = 0;

        if (player != null) {
            cuilianLevel = cn.winde.cuilian.Cuilian.checkPlayerZBCL(player);
            if (cuilianLevel > 0) {
                // 从配置文件获取淬炼属性加成
                cuilianAttack = cn.winde.cuilian.Cuilian.Weapon.getInt("shanghai." + cuilianLevel, 0);
                cuilianDefense = cn.winde.cuilian.Cuilian.Weapon.getInt("Defense." + cuilianLevel, 0);
                cuilianVampire = cn.winde.cuilian.Cuilian.Weapon.getInt("xixue." + cuilianLevel, 0);
                cuilianJump = cn.winde.cuilian.Cuilian.Weapon.getInt("jump." + cuilianLevel, 0);
            }
        }

        // 获取套装基础属性
        cn.winde.cuilian.suit.SuitManager.SuitAttribute attribute = cn.winde.cuilian.suit.SuitManager
                .getSuitAttribute(suitName);
        if (attribute == null) {
            // 如果没有套装属性，使用原有的更新方法
            updateSuitEffectLore(lore, suitName, isActivated);
            return;
        }

        for (int i = 0; i < lore.size(); i++) {
            String line = lore.get(i);

            // 更新套装效果状态行
            if (line.contains("套装效果:") && (line.contains("✓ 已激活") || line.contains("✗ 未激活"))) {
                lore.set(i, config.statusText);
            }
            // 更新攻击伤害属性行（合并淬炼属性）
            else if (line.contains("- 攻击伤害")) {
                int totalAttack = attribute.attackDamage + cuilianAttack;
                lore.set(i, config.attackDamageColor + "- 攻击伤害 +" + totalAttack);
            }
            // 更新防御力属性行（合并淬炼属性）
            else if (line.contains("- 防御力")) {
                int totalDefense = attribute.defense + cuilianDefense;
                lore.set(i, config.defenseColor + "- 防御力 +" + totalDefense);
            }
            // 更新跳跃高度属性行（合并淬炼属性）
            else if (line.contains("- 跳跃高度")) {
                int totalJump = attribute.jump + cuilianJump;
                lore.set(i, config.jumpColor + "- 跳跃高度 +" + totalJump);
            }
            // 更新吸血属性行（合并淬炼属性）
            else if (line.contains("- 吸血")) {
                int totalVampire = attribute.vampire + cuilianVampire;
                lore.set(i, config.vampireColor + "- 吸血 +" + totalVampire + "%");
            }
            // 更新其他属性行（不受淬炼影响）
            else if (line.contains("- 生命值")) {
                String cleanLine = line.replaceAll("^§[a-f0-9]", "");
                lore.set(i, config.healthColor + cleanLine);
            } else if (line.contains("- 移动速度")) {
                String cleanLine = line.replaceAll("^§[a-f0-9]", "");
                lore.set(i, config.speedColor + cleanLine);
            } else if (line.contains("特效")) {
                String cleanLine = line.replaceAll("^§[a-f0-9]", "");
                lore.set(i, config.effectColor + cleanLine);
            }
            // 更新提示信息行
            else if (line.contains("✓ 套装效果已激活") || line.contains("※ 需要穿戴完整套装")) {
                lore.set(i, config.tipText);
            }
        }

        // 添加或更新淬炼等级信息
        if (cuilianLevel > 0) {
            boolean hasLevelInfo = false;
            for (int i = 0; i < lore.size(); i++) {
                if (lore.get(i).contains("淬炼等级:")) {
                    lore.set(i, "§6§l- 淬炼等级: " + cuilianLevel + "星");
                    hasLevelInfo = true;
                    break;
                }
            }
            if (!hasLevelInfo) {
                // 在提示信息前添加淬炼等级
                for (int i = 0; i < lore.size(); i++) {
                    if (lore.get(i).contains("✓ 套装效果已激活") || lore.get(i).contains("※ 需要穿戴完整套装")) {
                        lore.add(i, "§6§l- 淬炼等级: " + cuilianLevel + "星");
                        break;
                    }
                }
            }
        }
    }

    /**
     * 从物品中获取套装名称
     *
     * @param item 物品
     * @return 套装名称，如果不是套装物品则返回null
     */
    private String getSuitNameFromItem(ItemStack item) {
        if (!item.hasItemMeta() || !item.getItemMeta().hasLore()) {
            return null;
        }

        List<String> lore = item.getItemMeta().getLore();
        for (String line : lore) {
            if (line.contains("§7§l套装: §e§l")) {
                // 提取套装名称
                return line.replace("§7§l套装: §e§l", "").trim();
            }
        }

        return null;
    }

}
