package cn.winde.cuilian;

import cn.winde.cuilian.suit.SuitManager;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryDragEvent;
import org.bukkit.event.inventory.InventoryType;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerItemBreakEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.PlayerRespawnEvent;
import org.bukkit.event.player.PlayerItemHeldEvent;
import org.bukkit.event.player.PlayerPickupItemEvent;
import org.bukkit.event.player.PlayerDropItemEvent;
import org.bukkit.event.block.Action;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.PlayerInventory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 监听玩家装备变更事件，更新特效状态
 */
public class ArmorChangeListener implements Listener {

    // 防抖机制：记录玩家最后一次检测的时间和套装等级
    private final Map<String, Long> lastCheckTime = new HashMap<>();
    private final Map<String, Integer> lastSuitLevel = new HashMap<>();
    private static final long CHECK_COOLDOWN = 100; // 100ms冷却时间

    /**
     * 当玩家点击物品栏时检查装备变更
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onInventoryClick(InventoryClickEvent event) {
        // 确保是玩家的物品栏且事件没有被取消
        if (event.isCancelled()) {
            return;
        }

        if (event.getWhoClicked() instanceof Player) {
            Player player = (Player) event.getWhoClicked();

            // 检查是否是装备槽位或者是玩家背包
            boolean isArmorSlot = event.getSlotType() == InventoryType.SlotType.ARMOR;
            boolean isPlayerInventory = event.getClickedInventory() instanceof PlayerInventory;

            // 检查是否涉及装备物品
            boolean involvesArmor = false;
            if (event.getCurrentItem() != null) {
                String typeName = event.getCurrentItem().getType().name();
                involvesArmor = typeName.endsWith("_HELMET") || typeName.endsWith("_CHESTPLATE") ||
                        typeName.endsWith("_LEGGINGS") || typeName.endsWith("_BOOTS");
            }
            if (event.getCursor() != null) {
                String typeName = event.getCursor().getType().name();
                involvesArmor = involvesArmor || typeName.endsWith("_HELMET") || typeName.endsWith("_CHESTPLATE") ||
                        typeName.endsWith("_LEGGINGS") || typeName.endsWith("_BOOTS");
            }

            // 如果涉及装备槽位或装备物品，延迟检查
            if (isArmorSlot || (isPlayerInventory && involvesArmor)) {
                // 立即检查一次
                Cuilian.getInstance().getServer().getScheduler().runTask(
                        Cuilian.getInstance(),
                        () -> updatePlayerEffect(player));

                // 再延迟1个tick检查一次，确保装备已经更新
                Cuilian.getInstance().getServer().getScheduler().runTaskLater(
                        Cuilian.getInstance(),
                        () -> updatePlayerEffect(player),
                        1L);

                // 最后延迟3个tick再检查一次，以防万一
                Cuilian.getInstance().getServer().getScheduler().runTaskLater(
                        Cuilian.getInstance(),
                        () -> updatePlayerEffect(player),
                        3L);
            }
        }
    }

    /**
     * 当玩家拖拽物品时检查装备变更
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onInventoryDrag(InventoryDragEvent event) {
        // 确保是玩家且事件没有被取消
        if (event.isCancelled() || !(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();

        // 检查拖拽的物品是否是装备
        if (event.getOldCursor() != null) {
            String typeName = event.getOldCursor().getType().name();
            boolean isArmor = typeName.endsWith("_HELMET") || typeName.endsWith("_CHESTPLATE") ||
                    typeName.endsWith("_LEGGINGS") || typeName.endsWith("_BOOTS");

            if (isArmor) {
                // 延迟2个tick检查装备，确保装备已经更新
                Cuilian.getInstance().getServer().getScheduler().runTaskLater(
                        Cuilian.getInstance(),
                        () -> updatePlayerEffect(player),
                        2L);
            }
        }
    }

    /**
     * 当玩家右键装备时检查装备变更
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerInteract(PlayerInteractEvent event) {
        // 确保是右键且事件没有被取消
        if (event.isCancelled() ||
                (event.getAction() != Action.RIGHT_CLICK_AIR && event.getAction() != Action.RIGHT_CLICK_BLOCK)) {
            return;
        }

        Player player = event.getPlayer();

        // 检查手中的物品是否是装备
        if (event.getItem() != null) {
            String typeName = event.getItem().getType().name();
            boolean isArmor = typeName.endsWith("_HELMET") || typeName.endsWith("_CHESTPLATE") ||
                    typeName.endsWith("_LEGGINGS") || typeName.endsWith("_BOOTS");

            if (isArmor) {
                // 立即检查一次
                Cuilian.getInstance().getServer().getScheduler().runTask(
                        Cuilian.getInstance(),
                        () -> updatePlayerEffect(player));

                // 延迟1个tick检查一次，确保装备已经更新
                Cuilian.getInstance().getServer().getScheduler().runTaskLater(
                        Cuilian.getInstance(),
                        () -> updatePlayerEffect(player),
                        1L);

                // 延迟3个tick再检查一次，以防万一
                Cuilian.getInstance().getServer().getScheduler().runTaskLater(
                        Cuilian.getInstance(),
                        () -> updatePlayerEffect(player),
                        3L);
            }
        }
    }

    /**
     * 当玩家关闭背包时检查装备变更
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onInventoryClose(InventoryCloseEvent event) {
        // 确保是玩家
        if (event.getPlayer() instanceof Player) {
            Player player = (Player) event.getPlayer();

            // 延迟1个tick检查装备，确保背包已经关闭
            Cuilian.getInstance().getServer().getScheduler().runTaskLater(
                    Cuilian.getInstance(),
                    () -> updatePlayerEffect(player),
                    1L);
        }
    }

    /**
     * 当玩家加入游戏时检查装备
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        // 延迟10个tick检查装备，确保玩家完全加载
        Cuilian.getInstance().getServer().getScheduler().runTaskLater(
                Cuilian.getInstance(),
                () -> updatePlayerEffect(event.getPlayer()),
                10L);
    }

    /**
     * 当玩家装备损坏时检查装备
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onItemBreak(PlayerItemBreakEvent event) {
        // 延迟1个tick检查装备，确保装备已经更新
        Cuilian.getInstance().getServer().getScheduler().runTask(
                Cuilian.getInstance(),
                () -> updatePlayerEffect(event.getPlayer()));
    }

    /**
     * 当玩家死亡时移除特效
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerDeath(PlayerDeathEvent event) {
        if (Cuilian.lizi.containsKey(event.getEntity().getName())) {
            Cuilian.lizi.remove(event.getEntity().getName());
        }
    }

    /**
     * 当玩家重生时检查装备
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerRespawn(PlayerRespawnEvent event) {
        // 延迟10个tick检查装备，确保玩家完全重生
        Cuilian.getInstance().getServer().getScheduler().runTaskLater(
                Cuilian.getInstance(),
                () -> updatePlayerEffect(event.getPlayer()),
                10L);
    }

    /**
     * 当玩家退出游戏时清理数据
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerQuit(PlayerQuitEvent event) {
        String playerName = event.getPlayer().getName();
        SuitManager.cleanupPlayer(playerName);
        if (Cuilian.lizi.containsKey(playerName)) {
            Cuilian.lizi.remove(playerName);
        }
        // 清理防抖缓存
        lastCheckTime.remove(playerName);
        lastSuitLevel.remove(playerName);
    }

    /**
     * 当玩家切换手持物品时检查套装（修复：添加延迟确保检测准确）
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerItemHeld(PlayerItemHeldEvent event) {
        Player player = event.getPlayer();
        String playerName = player.getName();

        // 首先检查命名套装（Suit.yml）- 不受手持武器影响
        String namedSuit = SuitManager.getPlayerSuit(playerName);
        if (namedSuit != null) {
            // 命名套装不受手持武器影响，直接返回
            return;
        }

        // 对于淬炼等级套装，需要延迟检测确保手持物品已更新
        Cuilian.getInstance().getServer().getScheduler().runTaskLater(
                Cuilian.getInstance(),
                () -> {
                    // 检查玩家是否正在预览套装，如果是则跳过特效更新
                    if (cn.winde.cuilian.preview.SuitPreviewManager.isPlayerPreviewing(playerName)) {
                        return;
                    }

                    // 防抖机制：检查是否需要更新
                    long currentTime = System.currentTimeMillis();
                    Long lastTime = lastCheckTime.get(playerName);
                    if (lastTime != null && (currentTime - lastTime) < CHECK_COOLDOWN) {
                        return; // 太频繁，跳过这次检测
                    }

                    Cuilian cuilianInstance = Cuilian.getInstance();
                    if (cuilianInstance != null) {
                        int zbdj = Cuilian.checkPlayerZBCL(player); // 检测穿戴装备等级
                        int wqdj = Cuilian.checkPlayerWQCL(player); // 检测手持武器等级
                        int tzdj = Cuilian.getCLTZ(zbdj, wqdj); // 计算套装等级（需要装备+武器）

                        // 检查套装等级是否有变化
                        Integer lastLevel = lastSuitLevel.get(playerName);
                        if (lastLevel != null && lastLevel.equals(tzdj)) {
                            return; // 套装等级没有变化，跳过
                        }

                        // 添加调试信息
                        if (player.isOp()) {
                            player.sendMessage("§7[调试] 装备等级: " + zbdj + ", 武器等级: " + wqdj + ", 套装等级: " + tzdj);
                        }

                        // 更新记录
                        lastCheckTime.put(playerName, currentTime);
                        lastSuitLevel.put(playerName, tzdj);

                        cuilianInstance.jihuoTZXG(player, tzdj); // 激活特效
                    }
                },
                2L); // 延迟2tick确保物品已更新
    }

    /**
     * 当玩家拾取物品时检查套装（修复：正确处理命名套装和淬炼套装）
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerPickupItem(PlayerPickupItemEvent event) {
        Player player = event.getPlayer();
        ItemStack pickedItem = event.getItem().getItemStack();
        String playerName = player.getName();

        // 检查拾取的是否是装备
        if (pickedItem != null && pickedItem.hasItemMeta() && pickedItem.getItemMeta().hasLore()) {
            String typeName = pickedItem.getType().name();
            boolean isArmor = typeName.endsWith("_HELMET") || typeName.endsWith("_CHESTPLATE") ||
                    typeName.endsWith("_LEGGINGS") || typeName.endsWith("_BOOTS");

            // 只有拾取装备时才需要检查套装状态
            if (isArmor) {
                // 延迟检查，确保物品已经进入背包
                Cuilian.getInstance().getServer().getScheduler().runTaskLater(
                        Cuilian.getInstance(),
                        () -> {
                            // 检查玩家是否正在预览套装，如果是则跳过特效更新
                            if (cn.winde.cuilian.preview.SuitPreviewManager.isPlayerPreviewing(playerName)) {
                                return;
                            }

                            // 首先检查命名套装（Suit.yml）
                            SuitManager.updatePlayerSuit(player);
                            String namedSuit = SuitManager.getPlayerSuit(playerName);
                            if (namedSuit != null) {
                                // 如果有命名套装，套装管理器已经处理了特效激活
                                return;
                            }

                            // 对于淬炼等级套装，需要检测装备+武器
                            Cuilian cuilianInstance = Cuilian.getInstance();
                            if (cuilianInstance != null) {
                                int zbdj = Cuilian.checkPlayerZBCL(player); // 检测穿戴装备等级
                                int wqdj = Cuilian.checkPlayerWQCL(player); // 检测手持武器等级
                                int tzdj = Cuilian.getCLTZ(zbdj, wqdj); // 计算套装等级（需要装备+武器）
                                cuilianInstance.jihuoTZXG(player, tzdj); // 激活特效
                            }
                        },
                        1L);
            }
        }
    }

    /**
     * 当玩家丢弃物品时检查套装（参考5.6版本）
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerDropItem(PlayerDropItemEvent event) {
        Player player = event.getPlayer();
        ItemStack droppedItem = event.getItemDrop().getItemStack();

        if (droppedItem.hasItemMeta() && droppedItem.getItemMeta().hasLore()) {
            // 延迟1个tick检查装备，确保物品已经丢弃
            Cuilian.getInstance().getServer().getScheduler().runTask(
                    Cuilian.getInstance(),
                    () -> updatePlayerEffect(player));
        }
    }

    /**
     * 更新玩家的特效状态（修复：正确处理命名套装和淬炼套装）
     */
    private void updatePlayerEffect(Player player) {
        String playerName = player.getName();

        // 检查玩家是否正在预览套装，如果是则跳过特效更新
        if (cn.winde.cuilian.preview.SuitPreviewManager.isPlayerPreviewing(playerName)) {
            return;
        }

        // 首先检查命名套装（Suit.yml）
        SuitManager.updatePlayerSuit(player);
        String namedSuit = SuitManager.getPlayerSuit(playerName);

        if (namedSuit != null) {
            // 如果有命名套装，套装管理器已经处理了特效激活
            updatePlayerArmorLore(player);
            return;
        }

        // 对于淬炼等级套装，需要检测装备+武器
        Cuilian cuilianInstance = Cuilian.getInstance();
        if (cuilianInstance != null) {
            int zbdj = Cuilian.checkPlayerZBCL(player); // 检测穿戴装备等级
            int wqdj = Cuilian.checkPlayerWQCL(player); // 检测手持武器等级
            int tzdj = Cuilian.getCLTZ(zbdj, wqdj); // 计算套装等级（需要装备+武器）
            cuilianInstance.jihuoTZXG(player, tzdj); // 激活特效
        }

        // 更新玩家装备的lore显示
        updatePlayerArmorLore(player);
    }

    /**
     * 更新玩家装备的lore显示
     */
    private void updatePlayerArmorLore(Player player) {
        // 更新穿戴的装备
        updateArmorSlot(player, player.getInventory().getHelmet(), "helmet");
        updateArmorSlot(player, player.getInventory().getChestplate(), "chestplate");
        updateArmorSlot(player, player.getInventory().getLeggings(), "leggings");
        updateArmorSlot(player, player.getInventory().getBoots(), "boots");

        // 更新背包中的淬炼装备 - 只更新特效状态，不重新生成装备
        for (int i = 0; i < player.getInventory().getSize(); i++) {
            ItemStack item = player.getInventory().getItem(i);
            if (item != null && item.getType() != Material.AIR && Cuilian.isCuilianEquipment(item)) {
                // 检查是否是装备类型
                String typeName = item.getType().name();
                boolean isArmor = typeName.endsWith("_HELMET") || typeName.endsWith("_CHESTPLATE") ||
                        typeName.endsWith("_LEGGINGS") || typeName.endsWith("_BOOTS");

                if (isArmor) {
                    int level = Cuilian.getItemCuilianLevel(item);
                    if (level > 0) {
                        // 只更新特效状态，不重新生成整个装备
                        ItemStack updatedItem = updateItemEffectStatus(item, level, player);
                        if (updatedItem != null) {
                            player.getInventory().setItem(i, updatedItem);
                        }
                    }
                }
            }
        }
    }

    /**
     * 只更新装备的特效状态，不重新生成整个装备
     *
     * @param item   装备物品
     * @param level  装备等级
     * @param player 玩家
     * @return 更新后的装备，如果无需更新则返回null
     */
    private ItemStack updateItemEffectStatus(ItemStack item, int level, Player player) {
        if (item == null || !item.hasItemMeta() || !item.getItemMeta().hasLore()) {
            return null;
        }

        ItemMeta meta = item.getItemMeta();
        ArrayList<String> lore = new ArrayList<>(meta.getLore());

        // 获取装备的实际等级
        int actualLevel = Cuilian.getItemCuilianLevel(item);

        // 检查是否已经有特效状态信息
        boolean hasEffectStatus = false;
        for (String line : lore) {
            if (line.contains("特效状态:")) {
                hasEffectStatus = true;
                break;
            }
        }

        // 如果没有特效状态信息，不需要更新
        if (!hasEffectStatus) {
            return null;
        }

        // 使用实际解析的等级而不是传入的等级
        updateEffectStatusInLore(lore, actualLevel, player);

        // 创建新的物品
        ItemStack newItem = item.clone();
        ItemMeta newMeta = newItem.getItemMeta();
        newMeta.setLore(lore);
        newItem.setItemMeta(newMeta);

        return newItem;
    }

    /**
     * 精确更新lore中的特效状态部分，不影响其他内容
     *
     * @param lore   lore列表
     * @param level  装备等级
     * @param player 玩家
     */
    private void updateEffectStatusInLore(ArrayList<String> lore, int level, Player player) {
        // 找到特效状态部分的开始和结束位置
        int effectStartIndex = -1;
        int effectEndIndex = -1;

        for (int i = 0; i < lore.size(); i++) {
            String line = lore.get(i);
            if (line.contains("特效状态:")) {
                effectStartIndex = i - 1; // 包括分隔线
                break;
            }
        }

        if (effectStartIndex == -1) {
            // 没有找到特效状态部分，无需处理
            return;
        }

        // 找到特效状态部分的结束位置
        for (int i = effectStartIndex + 1; i < lore.size(); i++) {
            String line = lore.get(i);
            // 如果遇到下一个部分的开始，或者是NBT标签，就停止
            if (line.startsWith("minecraft") || line.startsWith("NBT:") ||
                    (line.contains("§") && !isEffectStatusLine(line))) {
                effectEndIndex = i - 1;
                break;
            }
        }

        if (effectEndIndex == -1) {
            effectEndIndex = lore.size() - 1;
        }

        // 移除旧的特效状态部分
        for (int i = effectEndIndex; i >= effectStartIndex; i--) {
            if (i < lore.size()) {
                lore.remove(i);
            }
        }
    }

    /**
     * 判断是否是特效状态相关的行
     */
    private boolean isEffectStatusLine(String line) {
        return line.contains("特效状态") || line.contains("已激活") || line.contains("未激活") ||
                line.contains("还需") || line.contains("暂无激活的特效") || line.contains("已激活所有特效") ||
                line.contains("需要全套装备达到对应星级才能激活特效") || line.contains("套装效果") ||
                line.contains("需要穿戴完整") || line.contains("翅膀特效") || line.contains("光环特效") ||
                line.contains("火焰特效") || line.contains("星云特效") || line.contains("龙卷风特效") ||
                line.contains("星星特效") || line.contains("未知特效") || line.contains("雷电特效") ||
                line.contains("彩虹特效") || line.contains("时空裂缝特效") || line.contains("冰霜特效") ||
                line.contains("暗影特效") || line.contains("下一个") || line.contains("使用 /cuilian") ||
                line.contains("effect status") || line.contains("查看详细状态") || line.contains("查看详情") ||
                line.contains("查看状态") || line.contains("需要检测") || line.contains("已关闭") ||
                line.contains("开启特效显示") || line.contains("开启显示") || line.contains("当前套装等级") ||
                line.contains("星级才能激活特效") || line.contains("星以上才能激活特效") ||
                line.contains("装备达到") || line.contains("才能激活特效") ||
                line.contains("✓") || line.contains("✗") || line.contains("§2§l✓") || line.contains("§7§l✗") ||
                (line.contains("§7§m") && line.length() < 30);
    }

    /**
     * 更新单个装备槽位（只更新特效状态，不重新生成装备）
     */
    private void updateArmorSlot(Player player, ItemStack item, String slotType) {
        if (item != null && item.getType() != Material.AIR && Cuilian.isCuilianEquipment(item)) {
            int level = Cuilian.getItemCuilianLevel(item);
            if (level > 0) {
                // 只更新特效状态，不重新生成整个装备
                ItemStack updatedItem = updateItemEffectStatus(item, level, player);
                if (updatedItem != null) {
                    switch (slotType) {
                        case "helmet":
                            player.getInventory().setHelmet(updatedItem);
                            break;
                        case "chestplate":
                            player.getInventory().setChestplate(updatedItem);
                            break;
                        case "leggings":
                            player.getInventory().setLeggings(updatedItem);
                            break;
                        case "boots":
                            player.getInventory().setBoots(updatedItem);
                            break;
                    }
                }
            }
        }
    }
}
