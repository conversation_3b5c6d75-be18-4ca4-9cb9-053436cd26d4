package cn.winde.cuilian.preview;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerQuitEvent;

/**
 * 套装预览监听器
 * 处理玩家离线时的预览状态清理
 */
public class SuitPreviewListener implements Listener {

    /**
     * 玩家离线时清理预览状态
     */
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        String playerName = player.getName();
        
        // 清理玩家的预览状态
        if (SuitPreviewManager.isPlayerPreviewing(playerName)) {
            SuitPreviewManager.cleanupPlayerPreview(playerName);
        }
    }
}
