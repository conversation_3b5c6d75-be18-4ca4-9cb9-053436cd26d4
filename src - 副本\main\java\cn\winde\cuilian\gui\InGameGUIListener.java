package cn.winde.cuilian.gui;

import cn.winde.cuilian.preview.SuitPreviewManager;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import cn.winde.cuilian.suit.SuitManager;
import cn.winde.cuilian.util.SuitMessageManager;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 游戏内GUI界面事件监听器
 * 处理玩家在GUI界面中的点击事件
 */
public class InGameGUIListener implements Listener {

    // 跟踪打开特效设置界面的玩家
    private static final Set<String> playersWithEffectSettingsOpen = new HashSet<>();

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        String title = event.getInventory().getTitle();
        ItemStack clickedItem = event.getCurrentItem();

        // 检查是否是我们的GUI界面
        if (!isOurGUI(title)) {
            return;
        }

        // 取消事件，防止物品被拿走
        event.setCancelled(true);

        // 检查点击的物品是否有效
        if (clickedItem == null || !clickedItem.hasItemMeta() || !clickedItem.getItemMeta().hasDisplayName()) {
            return;
        }

        ItemMeta meta = clickedItem.getItemMeta();
        String displayName = meta.getDisplayName();

        // 处理不同界面的点击事件
        switch (title) {
            case "§6§l装备管理界面":
                handleEquipmentGUIClick(player, displayName);
                break;
            case "§6§l套装预览界面":
                handleSuitPreviewGUIClick(player, displayName, event);
                break;
            case "§6§l特效设置界面":
                handleEffectSettingsGUIClick(player, displayName, event);
                break;
            case "§6§l淬炼信息界面":
                handleEnhanceInfoGUIClick(player, displayName);
                break;
        }
    }

    /**
     * 检查是否是我们的GUI界面
     */
    private boolean isOurGUI(String title) {
        return title.equals("§6§l装备管理界面") ||
                title.equals("§6§l套装预览界面") ||
                title.equals("§6§l特效设置界面") ||
                title.equals("§6§l淬炼信息界面");
    }

    /**
     * 处理装备管理界面的点击事件
     */
    private void handleEquipmentGUIClick(Player player, String displayName) {
        switch (displayName) {
            case "§a§l特效设置":
                player.closeInventory();
                InGameGUI.openEffectSettingsGUI(player);
                break;
            case "§b§l套装预览":
                player.closeInventory();
                InGameGUI.openSuitPreviewGUI(player);
                break;
            case "§e§l淬炼信息":
                player.closeInventory();
                InGameGUI.openEnhanceInfoGUI(player);
                break;
        }
    }

    /**
     * 处理套装预览界面的点击事件
     */
    private void handleSuitPreviewGUIClick(Player player, String displayName, InventoryClickEvent event) {
        if (displayName.equals("§c§l返回")) {
            player.closeInventory();
            InGameGUI.openEquipmentGUI(player);
        } else if (displayName.startsWith("§6§l")) {
            // 点击了套装
            String suitName = displayName.substring(4); // 移除颜色代码

            // 检查点击类型
            if (event.getClick() == ClickType.LEFT) {
                // 左键点击：显示详细信息
                showSuitDetails(player, suitName);
            } else if (event.getClick() == ClickType.RIGHT) {
                // 右键点击：预览套装特效
                player.closeInventory(); // 关闭GUI
                boolean success = SuitPreviewManager.startPreview(player, suitName);
                if (!success) {
                    // 如果预览失败，重新打开GUI
                    InGameGUI.openSuitPreviewGUI(player);
                }
            }
        }
    }

    /**
     * 处理特效设置界面的点击事件
     */
    private void handleEffectSettingsGUIClick(Player player, String displayName, InventoryClickEvent event) {
        if (displayName.equals("§c§l返回")) {
            player.closeInventory();
            InGameGUI.openEquipmentGUI(player);
        } else if (displayName.equals("§a§l特效已开启") || displayName.equals("§c§l特效已关闭")) {
            // 切换特效状态
            boolean currentState = SuitManager.isPlayerEffectEnabled(player.getName());
            SuitManager.setPlayerEffectEnabled(player.getName(), !currentState);

            // 发送消息给玩家
            if (!currentState) {
                player.sendMessage("§a§l特效已开启！");
                // 特效开启时，重新计算并激活特效
                cn.winde.cuilian.Cuilian cuilianInstance = cn.winde.cuilian.Cuilian.getInstance();
                if (cuilianInstance != null) {
                    int zbdj = cn.winde.cuilian.Cuilian.checkPlayerZBCL(player); // 检测装备等级
                    int wqdj = cn.winde.cuilian.Cuilian.checkPlayerWQCL(player); // 检测武器等级
                    int tzdj = cn.winde.cuilian.Cuilian.getCLTZ(zbdj, wqdj); // 计算套装等级
                    cuilianInstance.jihuoTZXG(player, tzdj); // 激活特效
                }
            } else {
                player.sendMessage("§c§l特效已关闭！");
                // 特效关闭时，清除特效数据
                cn.winde.cuilian.Cuilian.lizi.remove(player.getName());
            }

            // 直接更新当前界面的物品，不关闭重开界面
            updateEffectSettingsGUI(player, event.getInventory());
        } else if (displayName.equals("§a§l消息提示已开启") || displayName.equals("§c§l消息提示已关闭")) {
            // 切换消息提示状态
            boolean currentMessageState = SuitMessageManager.isPlayerMessageEnabled(player.getName());
            SuitMessageManager.setPlayerMessageEnabled(player.getName(), !currentMessageState);

            // 发送消息给玩家
            if (!currentMessageState) {
                player.sendMessage("§a§l套装特效消息提示已开启！");
            } else {
                player.sendMessage("§c§l套装特效消息提示已关闭！");
            }

            // 直接更新当前界面的物品，不关闭重开界面
            updateEffectSettingsGUI(player, event.getInventory());
        }
    }

    /**
     * 更新特效设置界面的显示，不关闭界面
     */
    private static void updateEffectSettingsGUI(Player player, Inventory inventory) {
        // 更新特效开关按钮
        boolean effectEnabled = SuitManager.isPlayerEffectEnabled(player.getName());
        ItemStack effectToggle = createButton(
                effectEnabled ? Material.EMERALD_BLOCK : Material.REDSTONE_BLOCK,
                effectEnabled ? "§a§l特效已开启" : "§c§l特效已关闭",
                "§7点击切换特效状态");
        inventory.setItem(13, effectToggle);

        // 更新当前特效信息 - 烟花物品始终显示
        int currentLevel = cn.winde.cuilian.Cuilian.lizi.getOrDefault(player.getName(), 0);
        ItemStack currentEffect;

        if (currentLevel == -1) {
            // 命名套装特效
            String suitName = SuitManager.getPlayerSuit(player.getName());
            if (suitName != null) {
                currentEffect = createButton(
                        Material.FIREWORK,
                        "§d§l当前特效: " + suitName,
                        "§7当前激活的命名套装特效");
            } else {
                currentEffect = createButton(
                        Material.FIREWORK,
                        "§d§l当前特效: 命名套装",
                        "§7当前激活的命名套装特效");
            }
        } else if (currentLevel > 0) {
            // 淬炼套装特效
            currentEffect = createButton(
                    Material.FIREWORK,
                    "§d§l当前特效等级: " + currentLevel + "星",
                    "§7当前激活的淬炼套装特效");
        } else {
            // 没有激活的特效
            currentEffect = createMultiLineButton(
                    Material.FIREWORK,
                    "§7§l当前特效: 无",
                    "§7暂无激活的特效",
                    "§c需要穿戴6星以上套装或命名套装");
        }

        // 烟花物品始终显示在位置11
        inventory.setItem(11, currentEffect);

        // 更新消息提示开关的显示（仅在配置允许时显示）
        if (!SuitMessageManager.isForceDisabled()) {
            boolean messageEnabled = SuitMessageManager.isPlayerMessageEnabled(player.getName());
            ItemStack messageToggle = createButton(
                    messageEnabled ? Material.BOOK : Material.BOOK_AND_QUILL,
                    messageEnabled ? "§a§l消息提示已开启" : "§c§l消息提示已关闭",
                    "§7点击切换套装特效激活/失去消息");
            inventory.setItem(15, messageToggle);
        } else {
            // 如果强制禁用，清空位置15的物品
            inventory.setItem(15, null);
        }
    }

    /**
     * 创建功能按钮
     */
    private static ItemStack createButton(Material material, String name, String description) {
        ItemStack button = new ItemStack(material);
        ItemMeta meta = button.getItemMeta();

        if (meta != null) {
            meta.setDisplayName(name);
            List<String> lore = new ArrayList<>();
            lore.add(description);
            meta.setLore(lore);
            button.setItemMeta(meta);
        }

        return button;
    }

    /**
     * 创建多行描述的功能按钮
     */
    private static ItemStack createMultiLineButton(Material material, String name, String... descriptions) {
        ItemStack button = new ItemStack(material);
        ItemMeta meta = button.getItemMeta();

        if (meta != null) {
            meta.setDisplayName(name);
            List<String> lore = new ArrayList<>();
            for (String description : descriptions) {
                lore.add(description);
            }
            meta.setLore(lore);
            button.setItemMeta(meta);
        }

        return button;
    }

    /**
     * 配置重载通知方法
     * 当配置文件重载后，通知所有打开特效设置界面的玩家更新界面
     */
    public static void notifyConfigReload() {
        // 遍历所有打开特效设置界面的玩家
        for (String playerName : playersWithEffectSettingsOpen) {
            Player player = Bukkit.getPlayer(playerName);
            if (player != null && player.isOnline()) {
                // 检查玩家是否仍然打开着特效设置界面
                if (player.getOpenInventory() != null &&
                        player.getOpenInventory().getTitle().equals("§6§l特效设置界面")) {

                    // 直接更新界面物品，不关闭重开界面
                    Bukkit.getScheduler().runTask(cn.winde.cuilian.Cuilian.getInstance(), () -> {
                        updateEffectSettingsGUI(player, player.getOpenInventory().getTopInventory());
                    });
                }
            }
        }
    }

    /**
     * 添加玩家到特效设置界面跟踪列表
     */
    public static void addPlayerToEffectSettings(String playerName) {
        playersWithEffectSettingsOpen.add(playerName);
    }

    /**
     * 从特效设置界面跟踪列表中移除玩家
     */
    public static void removePlayerFromEffectSettings(String playerName) {
        playersWithEffectSettingsOpen.remove(playerName);
    }

    /**
     * 处理库存关闭事件
     * 当玩家关闭特效设置界面时，从跟踪列表中移除
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getPlayer();
        String title = event.getView().getTitle();

        // 如果关闭的是特效设置界面，从跟踪列表中移除玩家
        if (title.equals("§6§l特效设置界面")) {
            removePlayerFromEffectSettings(player.getName());
        }
    }

    /**
     * 处理淬炼信息界面的点击事件
     */
    private void handleEnhanceInfoGUIClick(Player player, String displayName) {
        if (displayName.equals("§c§l返回")) {
            player.closeInventory();
            InGameGUI.openEquipmentGUI(player);
        }
    }

    /**
     * 显示套装详细信息
     */
    private void showSuitDetails(Player player, String suitName) {
        player.sendMessage("§6§l=== " + suitName + " 详细信息 ===");

        // 从配置获取套装详细信息
        String basePath = "suit." + suitName;
        if (cn.winde.cuilian.Cuilian.Suit.contains(basePath)) {
            // 基本信息
            int quality = cn.winde.cuilian.Cuilian.Suit.getInt(basePath + ".quality", 1);
            player.sendMessage("§7品质等级: §e" + quality);

            // 属性信息
            int attack = cn.winde.cuilian.Cuilian.Suit.getInt(basePath + ".attribute.attack_damage", 0);
            int defense = cn.winde.cuilian.Cuilian.Suit.getInt(basePath + ".attribute.defense", 0);
            int health = cn.winde.cuilian.Cuilian.Suit.getInt(basePath + ".attribute.health", 0);
            double speed = cn.winde.cuilian.Cuilian.Suit.getDouble(basePath + ".attribute.speed", 0);
            int jump = cn.winde.cuilian.Cuilian.Suit.getInt(basePath + ".attribute.jump", 0);
            int vampire = cn.winde.cuilian.Cuilian.Suit.getInt(basePath + ".attribute.vampire", 0);

            player.sendMessage("§a§l套装属性:");
            if (attack > 0)
                player.sendMessage("§7- 攻击伤害: +" + attack);
            if (defense > 0)
                player.sendMessage("§7- 防御力: +" + defense);
            if (health > 0)
                player.sendMessage("§7- 生命值: +" + health);
            if (speed > 0)
                player.sendMessage("§7- 移动速度: +" + (speed * 100) + "%");
            if (jump > 0)
                player.sendMessage("§7- 跳跃高度: +" + jump);
            if (vampire > 0)
                player.sendMessage("§7- 吸血: +" + vampire + "%");

            // 装备名称
            player.sendMessage("§b§l套装装备:");
            String head = cn.winde.cuilian.Cuilian.Suit.getString(basePath + ".item.head", "");
            String chest = cn.winde.cuilian.Cuilian.Suit.getString(basePath + ".item.chest", "");
            String leg = cn.winde.cuilian.Cuilian.Suit.getString(basePath + ".item.leg", "");
            String foot = cn.winde.cuilian.Cuilian.Suit.getString(basePath + ".item.foot", "");

            if (!head.isEmpty())
                player.sendMessage("§7- 头盔: §e" + head);
            if (!chest.isEmpty())
                player.sendMessage("§7- 胸甲: §e" + chest);
            if (!leg.isEmpty())
                player.sendMessage("§7- 护腿: §e" + leg);
            if (!foot.isEmpty())
                player.sendMessage("§7- 靴子: §e" + foot);

            // 武器信息（如果有）
            String sword = cn.winde.cuilian.Cuilian.Suit.getString(basePath + ".item.weapon.sword", "");
            String bow = cn.winde.cuilian.Cuilian.Suit.getString(basePath + ".item.weapon.bow", "");
            if (!sword.isEmpty() || !bow.isEmpty()) {
                player.sendMessage("§c§l套装武器:");
                if (!sword.isEmpty())
                    player.sendMessage("§7- 剑: §e" + sword);
                if (!bow.isEmpty())
                    player.sendMessage("§7- 弓: §e" + bow);
            }

            // 特效信息
            showSuitEffectDetails(player, basePath);
        } else {
            player.sendMessage("§c§l套装信息不存在！");
        }

        player.sendMessage("§6§l========================");
    }

    /**
     * 显示套装特效详细信息
     *
     * @param player   玩家
     * @param basePath 配置路径
     */
    private void showSuitEffectDetails(Player player, String basePath) {
        try {
            String effectPath = basePath + ".effect";
            if (!cn.winde.cuilian.Cuilian.Suit.contains(effectPath)) {
                return;
            }

            player.sendMessage("§d§l特效信息:");

            // 检查是否有新格式的effects数组
            if (cn.winde.cuilian.Cuilian.Suit.contains(effectPath + ".effects")) {
                java.util.List<?> effectsList = cn.winde.cuilian.Cuilian.Suit.getList(effectPath + ".effects");
                boolean enableStacking = cn.winde.cuilian.Cuilian.Suit.getBoolean(effectPath + ".enable_stacking",
                        false);

                if (effectsList != null && !effectsList.isEmpty()) {
                    if (enableStacking && effectsList.size() > 1) {
                        player.sendMessage("§7- 支持多特效叠加 §e(共 " + effectsList.size() + " 种)");

                        for (int i = 0; i < effectsList.size(); i++) {
                            if (effectsList.get(i) instanceof java.util.Map) {
                                @SuppressWarnings("unchecked")
                                java.util.Map<String, Object> effectMap = (java.util.Map<String, Object>) effectsList
                                        .get(i);
                                String type = (String) effectMap.get("type");
                                boolean enabled = (Boolean) effectMap.getOrDefault("enabled", true);
                                String color1 = (String) effectMap.getOrDefault("colore1", "");
                                String color2 = (String) effectMap.getOrDefault("colore2", "");
                                String color3 = (String) effectMap.getOrDefault("colore3", "");

                                String displayName = SuitManager.getEffectDisplayName(type);
                                String statusText = enabled ? "§a启用" : "§c禁用";

                                player.sendMessage(String.format("§7  %d. §e%s §7[%s§7]",
                                        i + 1, displayName, statusText));

                                if (!color1.isEmpty() || !color2.isEmpty() || !color3.isEmpty()) {
                                    player.sendMessage(String.format("§7     颜色: §f%s§7, §f%s§7, §f%s",
                                            color1.isEmpty() ? "无" : color1,
                                            color2.isEmpty() ? "无" : color2,
                                            color3.isEmpty() ? "无" : color3));
                                }
                            }
                        }
                    } else {
                        // 单一特效（新格式）
                        if (effectsList.get(0) instanceof java.util.Map) {
                            @SuppressWarnings("unchecked")
                            java.util.Map<String, Object> effectMap = (java.util.Map<String, Object>) effectsList
                                    .get(0);
                            String type = (String) effectMap.get("type");
                            boolean enabled = (Boolean) effectMap.getOrDefault("enabled", true);
                            String color1 = (String) effectMap.getOrDefault("colore1", "");
                            String color2 = (String) effectMap.getOrDefault("colore2", "");
                            String color3 = (String) effectMap.getOrDefault("colore3", "");

                            String displayName = SuitManager.getEffectDisplayName(type);
                            String statusText = enabled ? "§a启用" : "§c禁用";

                            player.sendMessage(String.format("§7- 特效类型: §e%s §7[%s§7]", displayName, statusText));

                            if (!color1.isEmpty() || !color2.isEmpty() || !color3.isEmpty()) {
                                player.sendMessage(String.format("§7- 颜色配置: §f%s§7, §f%s§7, §f%s",
                                        color1.isEmpty() ? "无" : color1,
                                        color2.isEmpty() ? "无" : color2,
                                        color3.isEmpty() ? "无" : color3));
                            }
                        }
                    }
                }
            } else if (cn.winde.cuilian.Cuilian.Suit.contains(effectPath + ".type")) {
                // 旧格式的特效配置
                String type = cn.winde.cuilian.Cuilian.Suit.getString(effectPath + ".type", "");
                String color1 = cn.winde.cuilian.Cuilian.Suit.getString(effectPath + ".color1", "");
                String color2 = cn.winde.cuilian.Cuilian.Suit.getString(effectPath + ".color2", "");
                String color3 = cn.winde.cuilian.Cuilian.Suit.getString(effectPath + ".color3", "");

                if (!type.isEmpty()) {
                    String displayName = SuitManager.getEffectDisplayName(type);
                    player.sendMessage("§7- 特效类型: §e" + displayName + " §7[§a启用§7]");

                    if (!color1.isEmpty() || !color2.isEmpty() || !color3.isEmpty()) {
                        player.sendMessage(String.format("§7- 颜色配置: §f%s§7, §f%s§7, §f%s",
                                color1.isEmpty() ? "无" : color1,
                                color2.isEmpty() ? "无" : color2,
                                color3.isEmpty() ? "无" : color3));
                    }
                }
            }

        } catch (Exception e) {
            System.err.println("显示套装特效详细信息时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
