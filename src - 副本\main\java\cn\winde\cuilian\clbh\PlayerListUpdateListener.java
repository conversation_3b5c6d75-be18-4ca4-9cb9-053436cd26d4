package cn.winde.cuilian.clbh;

import org.bukkit.Bukkit;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import cn.winde.cuilian.Cuilian;

/**
 * 玩家加入/退出事件监听器
 * 用于实时更新玩家选择对话框中的在线玩家列表
 */
public class PlayerListUpdateListener implements Listener {

    /**
     * 玩家加入服务器事件
     */
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        // 立即通知所有打开的玩家选择对话框更新列表
        PlayerSelectionDialog.notifyPlayerListChanged();
    }

    /**
     * 玩家退出服务器事件
     */
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        // 玩家退出时需要延迟一点更新，因为在事件触发时玩家可能还在在线列表中
        Bukkit.getScheduler().runTaskLater(Cuilian.getInstance(), () -> {
            PlayerSelectionDialog.notifyPlayerListChanged();
        }, 1L); // 延迟1tick（50ms）确保玩家已从在线列表中移除
    }
}
