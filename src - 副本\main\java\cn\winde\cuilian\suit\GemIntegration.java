package cn.winde.cuilian.suit;

import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

/**
 * 宝石强化集成工具类
 * 用于将宝石强化系统集成到套装系统中
 */
public class GemIntegration {

    /**
     * 应用宝石强化等级到装备
     * @param item 要强化的装备
     * @param level 强化等级
     * @return 强化后的装备
     */
    public static ItemStack applyGemEnhancement(ItemStack item, int level) {
        if (item == null || level <= 0) {
            return item;
        }

        ItemStack enhancedItem = item.clone();
        ItemMeta meta = enhancedItem.getItemMeta();

        if (meta == null) {
            return enhancedItem;
        }

        // 获取原始名称（移除之前的强化信息）
        String originalName = meta.hasDisplayName() ? meta.getDisplayName() : item.getType().name();
        String cleanName = originalName.replaceAll("强化 \\+\\d+ ", "").trim();

        // 设置新的显示名称（与原始宝石插件风格一致）
        String newName = "强化 +" + level + " " + cleanName;
        meta.setDisplayName(newName);

        // 更新lore
        List<String> lore = meta.hasLore() ? new ArrayList<>(meta.getLore()) : new ArrayList<>();

        // 移除之前的强化符号和强化信息
        lore.removeIf(line -> line.contains("§c§l◆") || line.contains("§7§l◇") ||
                     line.contains("§c§l【强化信息】") || line.contains("§f§l等级:"));

        // 添加强化信息标题
        lore.add(0, "§c§l【强化信息】:");

        // 添加等级信息
        lore.add(1, "§f§l等级: §a§l+" + level + " §f§l级..");

        // 添加强化符号（与原始宝石插件一致）
        String symbols = getEnhancementSymbols(level);
        if (!symbols.isEmpty()) {
            lore.add(2, symbols);
        }

        meta.setLore(lore);

        // 应用附魔效果
        applyEnchantments(enhancedItem, meta, level);

        enhancedItem.setItemMeta(meta);
        return enhancedItem;
    }

    /**
     * 获取强化符号（与原始宝石插件风格一致）
     * @param level 当前等级
     * @return 强化符号字符串
     */
    private static String getEnhancementSymbols(int level) {
        if (level <= 0) {
            return "";
        }

        StringBuilder symbols = new StringBuilder();

        // 根据等级确定符号颜色（与原始宝石插件配置一致）
        String symbolColor = getSymbolColor(level);
        String withLevel = symbolColor + "§l◆";
        String withoutLevel = "§7§l◇";

        // 计算显示的符号数量（每10级显示10个符号）
        int displayMax = 10;
        int levelInTier = ((level - 1) % 10) + 1; // 在当前10级段中的等级

        // 添加已有等级的符号
        for (int i = 0; i < levelInTier; i++) {
            symbols.append(withLevel);
        }

        // 添加未有等级的符号
        for (int i = levelInTier; i < displayMax; i++) {
            symbols.append(withoutLevel);
        }

        return symbols.toString();
    }

    /**
     * 根据等级获取符号颜色（与原始宝石插件配置一致）
     * @param level 强化等级
     * @return 颜色代码
     */
    private static String getSymbolColor(int level) {
        if (level <= 10) return "§a";      // 1-10级：绿色
        else if (level <= 20) return "§b"; // 11-20级：青色
        else if (level <= 30) return "§e"; // 21-30级：黄色
        else if (level <= 40) return "§6"; // 31-40级：金色
        else if (level <= 50) return "§c"; // 41-50级：红色
        else if (level <= 60) return "§d"; // 51-60级：粉色
        else if (level <= 70) return "§5"; // 61-70级：紫色
        else if (level <= 80) return "§9"; // 71-80级：蓝色
        else if (level <= 90) return "§1"; // 81-90级：深蓝色
        else return "§4";                  // 91-100级：深红色
    }

    /**
     * 应用附魔效果（与原始宝石插件逻辑一致）
     * @param item 装备
     * @param meta 装备meta
     * @param level 强化等级
     */
    private static void applyEnchantments(ItemStack item, ItemMeta meta, int level) {
        Material type = item.getType();
        String typeName = type.name();

        // 清除现有附魔，重新应用
        for (Enchantment enchantment : meta.getEnchants().keySet()) {
            meta.removeEnchant(enchantment);
        }

        // 根据装备类型应用不同的附魔（只应用主要战斗附魔，不包括耐久）
        if (typeName.endsWith("_HELMET")) {
            // 头盔附魔：保护
            applyEnchantmentWithLevel(meta, Enchantment.PROTECTION_ENVIRONMENTAL, level);
        } else if (typeName.endsWith("_CHESTPLATE")) {
            // 胸甲附魔：保护
            applyEnchantmentWithLevel(meta, Enchantment.PROTECTION_ENVIRONMENTAL, level);
        } else if (typeName.endsWith("_LEGGINGS")) {
            // 护腿附魔：保护
            applyEnchantmentWithLevel(meta, Enchantment.PROTECTION_ENVIRONMENTAL, level);
        } else if (typeName.endsWith("_BOOTS")) {
            // 靴子附魔：保护
            applyEnchantmentWithLevel(meta, Enchantment.PROTECTION_ENVIRONMENTAL, level);
        } else if (typeName.endsWith("_SWORD")) {
            // 剑附魔：锋利
            applyEnchantmentWithLevel(meta, Enchantment.DAMAGE_ALL, level);
        } else if (typeName.equals("BOW")) {
            // 弓附魔：力量
            applyEnchantmentWithLevel(meta, Enchantment.ARROW_DAMAGE, level);
        } else if (typeName.endsWith("_PICKAXE") || typeName.endsWith("_AXE") ||
                   typeName.endsWith("_SHOVEL") || typeName.endsWith("_HOE")) {
            // 工具附魔：效率
            applyEnchantmentWithLevel(meta, Enchantment.DIG_SPEED, level);
        }
    }

    /**
     * 应用指定附魔和等级（突破原版附魔等级限制）
     * @param meta 装备meta
     * @param enchantment 附魔类型
     * @param gemLevel 宝石强化等级
     */
    private static void applyEnchantmentWithLevel(ItemMeta meta, Enchantment enchantment, int gemLevel) {
        // 直接使用宝石等级作为附魔等级
        // 使用 true 参数突破原版附魔等级限制，与原始宝石插件一致
        if (gemLevel > 0) {
            meta.addEnchant(enchantment, gemLevel, true);
        }
    }



    /**
     * 检查是否已经应用了宝石强化
     * @param item 装备
     * @return 是否已强化
     */
    public static boolean isGemEnhanced(ItemStack item) {
        if (item == null || !item.hasItemMeta() || !item.getItemMeta().hasDisplayName()) {
            return false;
        }

        String displayName = item.getItemMeta().getDisplayName();
        return displayName.contains("强化 +");
    }

    /**
     * 获取装备的强化等级
     * @param item 装备
     * @return 强化等级，如果没有强化则返回0
     */
    public static int getEnhancementLevel(ItemStack item) {
        if (!isGemEnhanced(item)) {
            return 0;
        }

        String displayName = item.getItemMeta().getDisplayName();
        try {
            // 查找"强化 +"的位置
            int enhanceIndex = displayName.indexOf("强化 +");
            if (enhanceIndex == -1) {
                return 0;
            }

            // 从"强化 +"后面开始提取数字
            int levelIndex = enhanceIndex + 3; // "强化 +"的长度是3
            String levelStr = "";

            for (int i = levelIndex; i < displayName.length(); i++) {
                char c = displayName.charAt(i);
                if (Character.isDigit(c)) {
                    levelStr += c;
                } else {
                    break;
                }
            }

            if (levelStr.isEmpty()) {
                return 0;
            }

            return Integer.parseInt(levelStr);
        } catch (Exception e) {
            return 0;
        }
    }
}
