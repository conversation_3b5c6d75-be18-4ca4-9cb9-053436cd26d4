/*
 * Decompiled with CFR 0.152.
 */
package cn.winde.cuilian.clbh;

import cn.winde.cuilian.Cuilian;
import cn.winde.cuilian.suit.SuitManager;
import java.awt.Color;
import java.awt.EventQueue;

import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.IOException;

import javax.swing.DefaultComboBoxModel;
import javax.swing.DefaultListModel;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JList;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JSplitPane;
import javax.swing.JTabbedPane;
import javax.swing.JTextField;
import javax.swing.border.EmptyBorder;
import javax.swing.BorderFactory;
import javax.swing.ListSelectionModel;

import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

// 添加玩家头颅显示所需的import
import javax.swing.ImageIcon;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.net.URL;
import java.util.concurrent.CompletableFuture;
import javax.imageio.ImageIO;

public class Mygui
        extends JFrame {
    private JPanel contentPane;
    private JCheckBox checkBox;

    // 玩家管理相关组件
    private PlayerGridPanel onlinePlayersGridPanel;
    private JTextField offlinePlayerField;
    private JComboBox<String> suitComboBox;
    private JComboBox<String> stoneComboBox;
    private JTextField stoneAmountField;

    // 套装创建相关组件
    private JTextField suitNameField;
    private JComboBox<String> qualityComboBox;
    private JTextField headNameField;
    private JTextField chestNameField;
    private JTextField legNameField;
    private JTextField footNameField;
    private JTextField swordNameField;
    private JTextField bowNameField;
    private JTextField attackField;
    private JTextField defenseField;
    private JTextField healthField;
    private JTextField speedField;
    private JTextField jumpField;
    private JTextField vampireField;
    // 宝石等级配置
    private JTextField headGemField;
    private JTextField chestGemField;
    private JTextField legGemField;
    private JTextField footGemField;
    private JTextField swordGemField;
    private JTextField bowGemField;
    // 描述配置
    private javax.swing.JTextArea descriptionArea;
    // 特效配置
    private JList<String> effectsList;
    private DefaultListModel<String> effectsModel;
    private JComboBox<String> effectTypeComboBox;
    private JComboBox<String> color1ComboBox;
    private JComboBox<String> color2ComboBox;
    private JComboBox<String> color3ComboBox;

    // 特效配置管理相关组件
    private JTextField newStarLevelField;
    private JComboBox<String> effectStarLevelComboBox;
    private JList<String> currentEffectsList;
    private DefaultListModel<String> currentEffectsModel;
    private JComboBox<String> availableEffectTypeComboBox;
    private JComboBox<String> effectColor1ComboBox;
    private JComboBox<String> effectColor2ComboBox;
    private JComboBox<String> effectColor3ComboBox;
    private JCheckBox effectEnabledCheckBox;
    private JLabel statusLabel; // 状态显示标签

    // 符咒管理相关组件
    private JComboBox<String> charmLevelComboBox;
    private JComboBox<String> charmTypeComboBox;
    private JTextField charmQuantityField;
    private JTextField charmTargetPlayerField;
    private PlayerGridPanel charmPlayersGridPanel;
    private DefaultListModel<String> charmOnlinePlayersModel;

    // 游戏内GUI管理相关组件
    private PlayerGridPanel guiPlayersGridPanel;
    private JTextField guiPlayerNameField;

    // TPS自动特效开关相关组件
    private JCheckBox tpsAutoEffectCheckBox;
    private JTextField tpsThresholdField;
    private JTextField tpsRecoveryDelayField;
    private JTextField previewDurationField;
    private JTextField previewCooldownField;
    private JTextField tpsDelayField;

    // 套装无限耐久开关
    private JCheckBox infiniteDurabilityCheckBox;

    // 套装特效消息强制禁用开关
    private JCheckBox forceDisableMessagesCheckBox;

    // 静态引用，用于玩家加入/退出事件的自动更新
    private static Mygui instance;

    // 套装删除相关组件
    private JComboBox<String> deleteSuitComboBox;

    // 星级配置相关组件
    private JTextField starLevelField;
    private JComboBox<String> existingStarLevelComboBox; // 现有星级选择
    // 属性配置
    private JTextField starAttackField;
    private JTextField starDefenseField;
    private JTextField starVampireField;
    private JTextField starJumpField;
    private JTextField starFallDamageField;
    private JTextField starCounterAttackField;
    // 强化石几率配置 - 针对当前星级的所有强化石类型
    private JTextField putongProbabilityField; // 普通强化石几率
    private JTextField zhongdengProbabilityField; // 中等强化石几率
    private JTextField gaodengProbabilityField; // 高等强化石几率
    private JTextField wanmeiProbabilityField; // 完美强化石几率

    public static void main(String[] args) {
        Mygui.setui();
    }

    public static void setui() {
        EventQueue.invokeLater(new Runnable() {

            @Override
            public void run() {
                try {
                    Mygui frame = new Mygui();
                    frame.checkBox.setSelected(Cuilian.config.getBoolean("effects"));
                    frame.setVisible(true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    public Mygui() {
        // 设置静态实例引用，用于玩家加入/退出事件的自动更新
        instance = this;

        // 设置现代化窗口属性
        this.setTitle("淬炼插件管理界面 v2.0");
        this.setDefaultCloseOperation(JFrame.HIDE_ON_CLOSE);
        this.setSize(1400, 900); // 增大界面尺寸以适应更大字体和组件
        this.setLocationRelativeTo(null); // 居中显示
        this.setResizable(true); // 允许调整大小

        // 创建主面板，使用BorderLayout实现自动调整
        this.contentPane = new JPanel(new java.awt.BorderLayout());
        this.contentPane.setBorder(new EmptyBorder(10, 10, 10, 10));
        this.contentPane.setBackground(java.awt.Color.WHITE);
        this.setContentPane(this.contentPane);

        // 创建标题面板
        JPanel titlePanel = createTitlePanel();
        this.contentPane.add(titlePanel, java.awt.BorderLayout.NORTH);

        // 创建主标签页面板
        JTabbedPane tabbedPane = new JTabbedPane(JTabbedPane.TOP);
        tabbedPane.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
        tabbedPane.setBackground(java.awt.Color.WHITE);
        this.contentPane.add(tabbedPane, java.awt.BorderLayout.CENTER);

        // 添加玩家管理标签页
        JPanel playerManagementPanel = createPlayerManagementPanel();
        tabbedPane.addTab("玩家管理", null, playerManagementPanel, null);

        // 添加套装创建标签页
        JPanel suitCreatorPanel = createSuitCreatorPanel();
        tabbedPane.addTab("套装创建", null, suitCreatorPanel, null);

        // 添加星级配置标签页
        JPanel starConfigPanel = createStarConfigPanel();
        tabbedPane.addTab("星级配置", null, starConfigPanel, null);

        // 特效配置标签页
        JPanel effectConfigPanel = createEffectConfigPanel();
        tabbedPane.addTab("特效配置", null, effectConfigPanel, null);

        // 符咒管理标签页
        JPanel charmPanel = createCharmManagementPanel();
        tabbedPane.addTab("符咒管理", null, charmPanel, null);

        // 游戏内GUI管理标签页
        JPanel inGameGUIPanel = createInGameGUIPanel();
        tabbedPane.addTab("游戏内GUI", null, inGameGUIPanel, null);

        // 创建底部面板
        JPanel bottomPanel = createBottomPanel();
        this.contentPane.add(bottomPanel, java.awt.BorderLayout.SOUTH);

        // 初始化数据
        initializeData();
    }

    /**
     * 静态方法：供外部事件监听器调用，用于自动更新在线玩家列表
     */
    public static void updateOnlinePlayersList() {
        if (instance != null) {
            // 在EDT线程中安全地刷新玩家列表
            javax.swing.SwingUtilities.invokeLater(() -> {
                instance.refreshOnlinePlayers();
                // 同时更新游戏内GUI面板的玩家列表
                instance.refreshGUIPlayersList();
            });
        }
    }

    /**
     * 刷新在线玩家列表
     */
    private void refreshOnlinePlayers() {
        java.util.List<String> players = new java.util.ArrayList<>();
        try {
            for (Player player : Bukkit.getOnlinePlayers()) {
                players.add(player.getName());
            }

            // 调试信息：打印实际获取到的玩家数量
            System.out.println("实际在线玩家数量: " + players.size());
            for (String playerName : players) {
                System.out.println("在线玩家: " + playerName);
            }

        } catch (Exception e) {
            // 如果Bukkit不可用，添加一些测试数据来验证布局
            System.out.println("Bukkit不可用，使用测试数据");
            for (int i = 1; i <= 25; i++) {
                players.add("测试玩家" + i);
            }
        }

        // 更新网格面板
        if (this.onlinePlayersGridPanel != null) {
            this.onlinePlayersGridPanel.updatePlayers(players);
        }
    }

    /**
     * 发送套装给玩家
     */
    private void sendSuitToPlayer() {
        String targetPlayer = getSelectedPlayer();
        if (targetPlayer == null || targetPlayer.isEmpty()) {
            JOptionPane.showMessageDialog(null, "请选择或输入玩家名称！");
            return;
        }

        String selectedSuit = (String) this.suitComboBox.getSelectedItem();
        if (selectedSuit == null || selectedSuit.equals("选择套装")) {
            JOptionPane.showMessageDialog(null, "请选择要发送的套装！");
            return;
        }

        try {
            Player player = Bukkit.getPlayer(targetPlayer);
            if (player != null && player.isOnline()) {
                // 在线玩家
                boolean success = SuitManager.giveSuitToPlayer(player, selectedSuit);
                if (success) {
                    JOptionPane.showMessageDialog(null, "成功给予玩家 " + targetPlayer + " 套装: " + selectedSuit);
                } else {
                    JOptionPane.showMessageDialog(null, "发送套装失败！套装可能不存在。");
                }
            } else {
                // 离线玩家
                @SuppressWarnings("deprecation")
                OfflinePlayer offlinePlayer = Bukkit.getOfflinePlayer(targetPlayer);
                if (offlinePlayer.hasPlayedBefore()) {
                    JOptionPane.showMessageDialog(null, "玩家 " + targetPlayer + " 当前离线，请等待玩家上线后手动发送。");
                } else {
                    JOptionPane.showMessageDialog(null, "玩家 " + targetPlayer + " 不存在！");
                }
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "发送套装时出现错误: " + e.getMessage());
        }
    }

    /**
     * 发送淬炼石给玩家
     */
    private void sendStoneToPlayer() {
        String targetPlayer = getSelectedPlayer();
        if (targetPlayer == null || targetPlayer.isEmpty()) {
            JOptionPane.showMessageDialog(null, "请选择或输入玩家名称！");
            return;
        }

        String stoneType = (String) this.stoneComboBox.getSelectedItem();
        if (stoneType == null) {
            JOptionPane.showMessageDialog(null, "请选择要发送的淬炼石类型！");
            return;
        }

        int amount;
        try {
            amount = Integer.parseInt(this.stoneAmountField.getText());
            if (amount <= 0) {
                JOptionPane.showMessageDialog(null, "数量必须大于0！");
                return;
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "请输入有效的数量！");
            return;
        }

        try {
            Player player = Bukkit.getPlayer(targetPlayer);
            if (player != null && player.isOnline()) {
                // 在线玩家
                ItemStack stone = createStoneByType(stoneType);
                if (stone != null) {
                    stone.setAmount(amount);
                    player.getInventory().addItem(stone);
                    JOptionPane.showMessageDialog(null,
                            "成功给予玩家 " + targetPlayer + " " + amount + "个 " + stoneType + "淬炼石");
                } else {
                    JOptionPane.showMessageDialog(null, "创建淬炼石失败！");
                }
            } else {
                // 离线玩家
                @SuppressWarnings("deprecation")
                OfflinePlayer offlinePlayer = Bukkit.getOfflinePlayer(targetPlayer);
                if (offlinePlayer.hasPlayedBefore()) {
                    JOptionPane.showMessageDialog(null, "玩家 " + targetPlayer + " 当前离线，请等待玩家上线后手动发送。");
                } else {
                    JOptionPane.showMessageDialog(null, "玩家 " + targetPlayer + " 不存在！");
                }
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "发送淬炼石时出现错误: " + e.getMessage());
        }
    }

    /**
     * 获取选中的玩家名称
     */
    private String getSelectedPlayer() {
        // 优先使用在线玩家网格面板中的选择
        if (this.onlinePlayersGridPanel != null) {
            String selectedOnline = this.onlinePlayersGridPanel.getSelectedPlayer();
            if (selectedOnline != null && !selectedOnline.isEmpty()) {
                return selectedOnline;
            }
        }

        // 如果没有选择在线玩家，使用离线玩家输入框
        String offlineInput = this.offlinePlayerField.getText().trim();
        if (!offlineInput.isEmpty()) {
            return offlineInput;
        }

        return null;
    }

    /**
     * 根据类型创建淬炼石
     */
    private ItemStack createStoneByType(String type) {
        switch (type) {
            case "普通":
                return Cuilian.clbs_putong();
            case "中等":
                return Cuilian.clbs_zhongdeng();
            case "高等":
                return Cuilian.clbs_gaodeng();
            case "上等":
                return Cuilian.clbs_wanmei();
            case "符咒":
                return Cuilian.clbs_yuangu();
            case "吞噬":
                return Cuilian.huaming();
            default:
                return null;
        }
    }

    /**
     * 添加特效到列表
     */
    private void addEffectToList() {
        String effectTypeDisplay = (String) this.effectTypeComboBox.getSelectedItem();
        String color1 = (String) this.color1ComboBox.getSelectedItem();
        String color2 = (String) this.color2ComboBox.getSelectedItem();
        String color3 = (String) this.color3ComboBox.getSelectedItem();

        if (effectTypeDisplay != null && color1 != null && color2 != null && color3 != null) {
            // 提取英文特效名称（去掉中文说明）
            String effectType = effectTypeDisplay.split(" - ")[0];
            String effectString = effectType + " (" + color1 + "," + color2 + "," + color3 + ")";
            this.effectsModel.addElement(effectString);
        }
    }

    /**
     * 从列表中删除特效
     */
    private void removeEffectFromList() {
        int selectedIndex = this.effectsList.getSelectedIndex();
        if (selectedIndex != -1) {
            this.effectsModel.removeElementAt(selectedIndex);
        } else {
            JOptionPane.showMessageDialog(null, "请先选择要删除的特效！");
        }
    }

    /**
     * 保存套装到配置文件
     */
    private void saveSuitToConfig() {
        String suitName = this.suitNameField.getText().trim();
        if (suitName.isEmpty()) {
            JOptionPane.showMessageDialog(null, "请输入套装名称！");
            return;
        }

        // 检查套装是否已存在
        if (Cuilian.Suit.getConfigurationSection("suit." + suitName) != null) {
            int result = JOptionPane.showConfirmDialog(null,
                    "套装 '" + suitName + "' 已存在，是否覆盖？",
                    "确认覆盖",
                    JOptionPane.YES_NO_OPTION);
            if (result != JOptionPane.YES_OPTION) {
                return;
            }
        }

        try {
            // 创建套装配置节点
            String basePath = "suit." + suitName;

            // 基本信息
            int quality = Integer.parseInt((String) this.qualityComboBox.getSelectedItem());
            Cuilian.Suit.set(basePath + ".quality", quality);

            // 装备名称
            Cuilian.Suit.set(basePath + ".item.head", this.headNameField.getText().trim());
            Cuilian.Suit.set(basePath + ".item.chest", this.chestNameField.getText().trim());
            Cuilian.Suit.set(basePath + ".item.leg", this.legNameField.getText().trim());
            Cuilian.Suit.set(basePath + ".item.foot", this.footNameField.getText().trim());

            // 武器（如果有输入）
            String swordName = this.swordNameField.getText().trim();
            String bowName = this.bowNameField.getText().trim();
            if (!swordName.isEmpty() || !bowName.isEmpty()) {
                if (!swordName.isEmpty()) {
                    Cuilian.Suit.set(basePath + ".item.weapon.sword", swordName);
                }
                if (!bowName.isEmpty()) {
                    Cuilian.Suit.set(basePath + ".item.weapon.bow", bowName);
                }
            }

            // 属性配置
            Cuilian.Suit.set(basePath + ".attribute.attack_damage", Integer.parseInt(this.attackField.getText()));
            Cuilian.Suit.set(basePath + ".attribute.defense", Integer.parseInt(this.defenseField.getText()));
            Cuilian.Suit.set(basePath + ".attribute.health", Integer.parseInt(this.healthField.getText()));
            Cuilian.Suit.set(basePath + ".attribute.speed", Double.parseDouble(this.speedField.getText()));
            Cuilian.Suit.set(basePath + ".attribute.jump", Integer.parseInt(this.jumpField.getText()));
            Cuilian.Suit.set(basePath + ".attribute.vampire", Integer.parseInt(this.vampireField.getText()));

            // 无限耐久配置
            Cuilian.Suit.set(basePath + ".attribute.infinite_durability", this.infiniteDurabilityCheckBox.isSelected());

            // 特效配置
            if (this.effectsModel.getSize() > 0) {
                Cuilian.Suit.set(basePath + ".effect.enable_stacking", true);

                // 创建特效列表
                java.util.List<java.util.Map<String, Object>> effectsList = new java.util.ArrayList<>();

                for (int i = 0; i < this.effectsModel.getSize(); i++) {
                    String effectString = this.effectsModel.getElementAt(i);
                    // 解析特效字符串: "effectType (color1,color2,color3)"
                    String[] parts = effectString.split(" \\(");
                    if (parts.length == 2) {
                        String effectType = parts[0];
                        String colorsPart = parts[1].replace(")", "");
                        String[] colors = colorsPart.split(",");

                        if (colors.length == 3) {
                            java.util.Map<String, Object> effectMap = new java.util.HashMap<>();
                            effectMap.put("type", effectType);
                            effectMap.put("enabled", true);
                            effectMap.put("colore1", colors[0]);
                            effectMap.put("colore2", colors[1]);
                            effectMap.put("colore3", colors[2]);
                            effectsList.add(effectMap);
                        }
                    }
                }

                Cuilian.Suit.set(basePath + ".effect.effects", effectsList);

                // 兼容旧格式（使用第一个特效）
                if (!effectsList.isEmpty()) {
                    java.util.Map<String, Object> firstEffect = effectsList.get(0);
                    Cuilian.Suit.set(basePath + ".effect.type", firstEffect.get("type"));
                    Cuilian.Suit.set(basePath + ".effect.color1", firstEffect.get("colore1"));
                    Cuilian.Suit.set(basePath + ".effect.color2", firstEffect.get("colore2"));
                    Cuilian.Suit.set(basePath + ".effect.color3", firstEffect.get("colore3"));
                }
            }

            // 宝石等级配置
            Cuilian.Suit.set(basePath + ".gem_levels.head", Integer.parseInt(this.headGemField.getText()));
            Cuilian.Suit.set(basePath + ".gem_levels.chest", Integer.parseInt(this.chestGemField.getText()));
            Cuilian.Suit.set(basePath + ".gem_levels.leg", Integer.parseInt(this.legGemField.getText()));
            Cuilian.Suit.set(basePath + ".gem_levels.foot", Integer.parseInt(this.footGemField.getText()));
            if (!swordName.isEmpty()) {
                Cuilian.Suit.set(basePath + ".gem_levels.sword", Integer.parseInt(this.swordGemField.getText()));
            }
            if (!bowName.isEmpty()) {
                Cuilian.Suit.set(basePath + ".gem_levels.bow", Integer.parseInt(this.bowGemField.getText()));
            }

            // 描述配置
            String descriptionText = this.descriptionArea.getText().trim();
            java.util.List<String> description = new java.util.ArrayList<>();
            if (!descriptionText.isEmpty()) {
                String[] lines = descriptionText.split("\n");
                for (String line : lines) {
                    if (!line.trim().isEmpty()) {
                        description.add(line.trim());
                    }
                }
            } else {
                // 如果没有输入描述，使用默认描述
                description.add("§6§l" + suitName + "套装");
                description.add("§e传说中的" + suitName.replace("套装", "") + "装者套装");
                description.add("§a拥有强大的力量和神秘的特效");
            }
            Cuilian.Suit.set(basePath + ".description", description);

            // 保存配置文件
            Cuilian.Suit.save(Cuilian.f5);

            JOptionPane.showMessageDialog(null, "套装 '" + suitName + "' 已成功保存到 Suit.yml！");

            // 自动更新所有相关的下拉列表
            updateAllSuitLists();

            // 清空表单
            clearSuitForm();

        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "请检查数值输入是否正确！");
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "保存套装时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 清空套装创建表单
     */
    private void clearSuitForm() {
        this.suitNameField.setText("");
        this.headNameField.setText("");
        this.chestNameField.setText("");
        this.legNameField.setText("");
        this.footNameField.setText("");
        this.swordNameField.setText("");
        this.bowNameField.setText("");
        this.attackField.setText("15");
        this.defenseField.setText("20");
        this.healthField.setText("40");
        this.speedField.setText("0.1");
        this.jumpField.setText("1");
        this.vampireField.setText("10");
        // 重置无限耐久开关
        this.infiniteDurabilityCheckBox.setSelected(false);
        // 清空宝石等级字段
        this.headGemField.setText("15");
        this.chestGemField.setText("15");
        this.legGemField.setText("15");
        this.footGemField.setText("15");
        this.swordGemField.setText("18");
        this.bowGemField.setText("16");
        // 清空描述
        this.descriptionArea.setText("§6§l自定义套装\n§e传说中的装者套装\n§a拥有强大的力量和神秘的特效");
        // 清空特效
        this.effectsModel.clear();
        this.qualityComboBox.setSelectedIndex(5);
        this.effectTypeComboBox.setSelectedIndex(0);
        this.color1ComboBox.setSelectedIndex(0);
        this.color2ComboBox.setSelectedIndex(1);
        this.color3ComboBox.setSelectedIndex(4);
    }

    /**
     * 动态加载可用套装列表
     */
    private void loadAvailableSuits() {
        this.suitComboBox.removeAllItems();
        this.suitComboBox.addItem("选择套装");

        try {
            // 从Suit.yml配置文件中读取所有套装
            if (Cuilian.Suit != null) {
                org.bukkit.configuration.ConfigurationSection suitSection = Cuilian.Suit
                        .getConfigurationSection("suit");
                if (suitSection != null) {
                    java.util.Set<String> suitNames = suitSection.getKeys(false);
                    for (String suitName : suitNames) {
                        this.suitComboBox.addItem(suitName);
                    }
                }
            }
        } catch (Exception e) {
            // 如果读取失败，添加一些默认套装
            this.suitComboBox.addItem("神武套装");
            this.suitComboBox.addItem("星辰套装");
            this.suitComboBox.addItem("暗影套装");
            System.out.println("加载套装列表时出现错误: " + e.getMessage());
        }
    }

    /**
     * 加载删除套装列表
     */
    private void loadDeleteSuitList() {
        this.deleteSuitComboBox.removeAllItems();
        this.deleteSuitComboBox.addItem("选择要删除的套装");

        try {
            // 从Suit.yml配置文件中读取所有套装
            if (Cuilian.Suit != null) {
                org.bukkit.configuration.ConfigurationSection suitSection = Cuilian.Suit
                        .getConfigurationSection("suit");
                if (suitSection != null) {
                    java.util.Set<String> suitNames = suitSection.getKeys(false);
                    for (String suitName : suitNames) {
                        this.deleteSuitComboBox.addItem(suitName);
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("加载删除套装列表时出现错误: " + e.getMessage());
        }
    }

    /**
     * 删除套装从配置文件
     */
    private void deleteSuitFromConfig() {
        String selectedSuit = (String) this.deleteSuitComboBox.getSelectedItem();
        if (selectedSuit == null || selectedSuit.equals("选择要删除的套装")) {
            JOptionPane.showMessageDialog(null, "请选择要删除的套装！");
            return;
        }

        // 确认删除
        int result = JOptionPane.showConfirmDialog(null,
                "确定要删除套装 '" + selectedSuit + "' 吗？\n此操作不可撤销！",
                "确认删除",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.WARNING_MESSAGE);

        if (result != JOptionPane.YES_OPTION) {
            return;
        }

        try {
            // 从配置文件中删除套装
            Cuilian.Suit.set("suit." + selectedSuit, null);

            // 保存配置文件
            Cuilian.Suit.save(Cuilian.f5);

            JOptionPane.showMessageDialog(null, "套装 '" + selectedSuit + "' 已成功删除！");

            // 自动更新所有相关的下拉列表
            updateAllSuitLists();

        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "删除套装时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 更新所有套装相关的下拉列表
     */
    private void updateAllSuitLists() {
        // 更新玩家管理页面的套装发送列表
        loadAvailableSuits();

        // 更新套装创建页面的删除套装列表
        loadDeleteSuitList();
    }

    /**
     * 保存星级配置到配置文件
     */
    private void saveStarConfiguration() {
        String newStarLevel = this.starLevelField.getText().trim();
        String selectedExisting = (String) this.existingStarLevelComboBox.getSelectedItem();

        int level;
        boolean isNewLevel = false;

        if (!newStarLevel.isEmpty()) {
            // 创建新星级
            try {
                level = Integer.parseInt(newStarLevel);
                if (level < 0) {
                    JOptionPane.showMessageDialog(null, "星级等级必须大于等于0！");
                    return;
                }
                isNewLevel = true;
            } catch (NumberFormatException e) {
                JOptionPane.showMessageDialog(null, "请输入有效的星级数字！");
                return;
            }
        } else if (selectedExisting != null && !selectedExisting.equals("选择现有星级")) {
            // 编辑现有星级
            try {
                String levelStr = selectedExisting.replace("星", "");
                level = Integer.parseInt(levelStr);
            } catch (NumberFormatException e) {
                JOptionPane.showMessageDialog(null, "解析现有星级时出现错误！");
                return;
            }
        } else {
            JOptionPane.showMessageDialog(null, "请输入新星级等级或选择现有星级进行编辑！");
            return;
        }

        try {

            // 星级配置只创建新的淬炼等级，不包含特效配置
            // 特效配置需要在config.yml中单独设置

            // 保存到Weapon.yml的属性部分
            int attack = Integer.parseInt(this.starAttackField.getText());
            int defense = Integer.parseInt(this.starDefenseField.getText());
            int vampire = Integer.parseInt(this.starVampireField.getText());
            int jump = Integer.parseInt(this.starJumpField.getText());
            int fallDamage = Integer.parseInt(this.starFallDamageField.getText());
            int counterAttack = Integer.parseInt(this.starCounterAttackField.getText());

            Cuilian.Weapon.set("shanghai." + level, attack);
            Cuilian.Weapon.set("Defense." + level, defense);
            Cuilian.Weapon.set("xixue." + level, vampire);
            Cuilian.Weapon.set("jump." + level, jump);
            Cuilian.Weapon.set("js." + level, fallDamage);
            Cuilian.Weapon.set("fs." + level, counterAttack);

            // 保存到Probability.yml的强化石几率部分 - 只针对当前星级
            int putongProb = Integer.parseInt(this.putongProbabilityField.getText());
            int zhongdengProb = Integer.parseInt(this.zhongdengProbabilityField.getText());
            int gaodengProb = Integer.parseInt(this.gaodengProbabilityField.getText());
            int wanmeiProb = Integer.parseInt(this.wanmeiProbabilityField.getText());

            Cuilian.Probability.set("putong." + level, putongProb);
            Cuilian.Probability.set("zhongdeng." + level, zhongdengProb);
            Cuilian.Probability.set("gaodeng." + level, gaodengProb);
            Cuilian.Probability.set("wanmei." + level, wanmeiProb);

            // 保存所有配置文件
            Cuilian.config.save(Cuilian.filess);
            Cuilian.Weapon.save(Cuilian.f);
            Cuilian.Probability.save(Cuilian.f6);

            // 重新加载配置到内存中的HashMap，确保新配置生效
            reloadConfigurationData();

            String actionType = isNewLevel ? "创建" : "更新";
            JOptionPane.showMessageDialog(null, "星级配置 " + level + "星 已成功" + actionType + "！\n配置已重新加载，可以立即使用。");

            // 自动更新现有星级列表
            loadExistingStarLevels();

            // 清空表单
            clearStarForm();

        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "请检查数值输入是否正确！");
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "保存星级配置时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 清空星级配置表单
     */
    private void clearStarForm() {
        this.starLevelField.setText("");
        this.existingStarLevelComboBox.setSelectedIndex(0);
        this.starAttackField.setText("0");
        this.starDefenseField.setText("0");
        this.starVampireField.setText("0");
        this.starJumpField.setText("0");
        this.starFallDamageField.setText("0");
        this.starCounterAttackField.setText("0");

        // 重置强化石几率为默认值
        this.putongProbabilityField.setText("100");
        this.zhongdengProbabilityField.setText("100");
        this.gaodengProbabilityField.setText("100");
        this.wanmeiProbabilityField.setText("100");
    }

    /**
     * 加载现有星级列表
     */
    private void loadExistingStarLevels() {
        this.existingStarLevelComboBox.removeAllItems();
        this.existingStarLevelComboBox.addItem("选择现有星级");

        try {
            // 从Weapon.yml中读取所有星级，使用TreeSet收集所有数字星级
            java.util.Set<Integer> starLevelNumbers = new java.util.TreeSet<>();

            if (Cuilian.Weapon != null) {
                // 从各个属性配置中收集星级
                org.bukkit.configuration.ConfigurationSection shanghaiSection = Cuilian.Weapon
                        .getConfigurationSection("shanghai");
                if (shanghaiSection != null) {
                    for (String level : shanghaiSection.getKeys(false)) {
                        try {
                            int levelNum = Integer.parseInt(level);
                            starLevelNumbers.add(levelNum);
                        } catch (NumberFormatException e) {
                            // 忽略非数字的配置项
                        }
                    }
                }

                org.bukkit.configuration.ConfigurationSection defenseSection = Cuilian.Weapon
                        .getConfigurationSection("Defense");
                if (defenseSection != null) {
                    for (String level : defenseSection.getKeys(false)) {
                        try {
                            int levelNum = Integer.parseInt(level);
                            starLevelNumbers.add(levelNum);
                        } catch (NumberFormatException e) {
                            // 忽略非数字的配置项
                        }
                    }
                }

                // 按数字从小到大的顺序添加到下拉列表
                for (Integer levelNum : starLevelNumbers) {
                    this.existingStarLevelComboBox.addItem(levelNum + "星");
                }
            }
        } catch (Exception e) {
            System.out.println("加载现有星级列表时出现错误: " + e.getMessage());
        }
    }

    /**
     * 加载选中星级的数据到表单
     */
    private void loadExistingStarLevelData() {
        String selectedItem = (String) this.existingStarLevelComboBox.getSelectedItem();
        if (selectedItem == null || selectedItem.equals("选择现有星级")) {
            return;
        }

        try {
            // 提取星级数字
            String levelStr = selectedItem.replace("星", "");
            int level = Integer.parseInt(levelStr);

            // 清空新建星级输入框
            this.starLevelField.setText("");

            // 加载属性数据
            if (Cuilian.Weapon != null) {
                this.starAttackField.setText(String.valueOf(Cuilian.Weapon.getInt("shanghai." + level, 0)));
                this.starDefenseField.setText(String.valueOf(Cuilian.Weapon.getInt("Defense." + level, 0)));
                this.starVampireField.setText(String.valueOf(Cuilian.Weapon.getInt("xixue." + level, 0)));
                this.starJumpField.setText(String.valueOf(Cuilian.Weapon.getInt("jump." + level, 0)));
                this.starFallDamageField.setText(String.valueOf(Cuilian.Weapon.getInt("js." + level, 0)));
                this.starCounterAttackField.setText(String.valueOf(Cuilian.Weapon.getInt("fs." + level, 0)));
            }

            // 加载强化石几率数据
            if (Cuilian.Probability != null) {
                this.putongProbabilityField.setText(String.valueOf(Cuilian.Probability.getInt("putong." + level, 100)));
                this.zhongdengProbabilityField
                        .setText(String.valueOf(Cuilian.Probability.getInt("zhongdeng." + level, 100)));
                this.gaodengProbabilityField
                        .setText(String.valueOf(Cuilian.Probability.getInt("gaodeng." + level, 100)));
                this.wanmeiProbabilityField.setText(String.valueOf(Cuilian.Probability.getInt("wanmei." + level, 100)));
            }

        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "解析星级数据时出现错误！");
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "加载星级数据时出现错误: " + e.getMessage());
        }
    }

    /**
     * 重新加载配置数据到内存中的HashMap
     */
    private void reloadConfigurationData() {
        try {
            // 重新加载Weapon.yml配置到HashMap
            if (Cuilian.Weapon != null) {
                cn.winde.cuilian.Loadlang.loadWeapon(Cuilian.Weapon);
            }

            // 重新加载Probability.yml配置到HashMap
            if (Cuilian.Probability != null) {
                cn.winde.cuilian.Loadlang.loadProbability(Cuilian.Probability);
            }

            System.out.println("配置数据已重新加载到内存");
        } catch (Exception e) {
            System.out.println("重新加载配置数据时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 重新加载特效配置到游戏中
     */
    private void reloadEffectConfiguration() {
        try {
            // 保存当前选中的星级
            String currentSelectedLevel = (String) this.effectStarLevelComboBox.getSelectedItem();

            // 重新加载config.yml配置文件
            if (Cuilian.filess != null && Cuilian.filess.exists()) {
                Cuilian.config = org.bukkit.configuration.file.YamlConfiguration.loadConfiguration(Cuilian.filess);
                System.out.println("特效配置已重新加载到游戏中");
            }

            // 刷新UI中的星级列表（可能有新增的星级）
            initializeEffectConfigData();

            // 恢复之前选中的星级
            if (currentSelectedLevel != null && !currentSelectedLevel.equals("选择星级")) {
                this.effectStarLevelComboBox.setSelectedItem(currentSelectedLevel);
            }

            // 重新加载当前选中星级的特效列表
            loadEffectsForSelectedLevel();

        } catch (Exception e) {
            System.out.println("重新加载特效配置时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 更新状态标签显示
     */
    private void updateStatusLabel(String message, boolean isSuccess) {
        if (this.statusLabel != null) {
            this.statusLabel.setText(message);
            if (isSuccess) {
                this.statusLabel.setForeground(new java.awt.Color(0, 128, 0)); // 绿色
            } else {
                this.statusLabel.setForeground(new java.awt.Color(255, 0, 0)); // 红色
            }

            // 3秒后恢复为就绪状态
            javax.swing.Timer timer = new javax.swing.Timer(3000, e -> {
                this.statusLabel.setText("就绪");
                this.statusLabel.setForeground(new java.awt.Color(0, 128, 0));
            });
            timer.setRepeats(false);
            timer.start();
        }
    }

    /**
     * 根据英文特效类型获取中文显示名称
     */
    private String getEffectTypeDisplayName(String effectType) {
        java.util.Map<String, String> effectTypeMap = new java.util.HashMap<>();

        // 淬炼5.6版本经典特效
        effectTypeMap.put("flame_basic", "flame_basic - 基础火焰");
        effectTypeMap.put("footstep", "footstep - 脚步");
        effectTypeMap.put("spiral_flame", "spiral_flame - 螺旋火焰");
        effectTypeMap.put("rotating_halo", "rotating_halo - 旋转光环");
        effectTypeMap.put("four_direction_spell", "four_direction_spell - 四方向法术");
        effectTypeMap.put("wings_56", "wings_56 - 5.6版本翅膀");

        // 现代版本基础特效
        effectTypeMap.put("wings", "wings - 翅膀");
        effectTypeMap.put("halo", "halo - 光环");
        effectTypeMap.put("flame", "flame - 火焰");
        effectTypeMap.put("nebula", "nebula - 星云");
        effectTypeMap.put("tornado", "tornado - 龙卷风");
        effectTypeMap.put("stars", "stars - 星星");

        // 现代版本几何特效
        effectTypeMap.put("spiral", "spiral - 螺旋");
        effectTypeMap.put("sphere", "sphere - 球体");
        effectTypeMap.put("orbit", "orbit - 轨道");
        effectTypeMap.put("cube", "cube - 立方体");
        effectTypeMap.put("helix", "helix - 螺旋桨");

        // 现代版本动态特效
        effectTypeMap.put("batman", "batman - 蝙蝠侠");
        effectTypeMap.put("popper", "popper - 爆炸螺旋");
        effectTypeMap.put("pulse", "pulse - 脉冲");
        effectTypeMap.put("whirl", "whirl - 旋涡");
        effectTypeMap.put("whirlwind", "whirlwind - 旋风");
        effectTypeMap.put("invocation", "invocation - 召唤法阵");

        // 现代版本高级特效
        effectTypeMap.put("lightning", "lightning - 雷电");
        effectTypeMap.put("rainbow", "rainbow - 彩虹");
        effectTypeMap.put("spacerift", "spacerift - 时空裂缝");
        effectTypeMap.put("frost", "frost - 冰霜");
        effectTypeMap.put("shadow", "shadow - 暗影");

        // PlayerParticles集成特效
        effectTypeMap.put("pp_wings", "pp_wings - PP翅膀");
        effectTypeMap.put("pp_beam", "pp_beam - PP光束");
        effectTypeMap.put("pp_quadhelix", "pp_quadhelix - PP四重螺旋");
        effectTypeMap.put("pp_swords", "pp_swords - PP剑击");

        return effectTypeMap.getOrDefault(effectType, effectType + " - 未知特效");
    }

    /**
     * 初始化特效配置数据
     */
    private void initializeEffectConfigData() {
        // 初始化星级选择下拉框
        this.effectStarLevelComboBox.removeAllItems();
        this.effectStarLevelComboBox.addItem("选择星级");

        // 动态读取已配置的星级
        try {
            if (Cuilian.config != null && Cuilian.config.isConfigurationSection("eff")) {
                org.bukkit.configuration.ConfigurationSection effSection = Cuilian.config
                        .getConfigurationSection("eff");
                java.util.Set<String> levelKeys = effSection.getKeys(false);

                // 提取数字并排序
                java.util.List<Integer> levelNumbers = new java.util.ArrayList<>();
                for (String key : levelKeys) {
                    if (key.startsWith("leve")) {
                        try {
                            int level = Integer.parseInt(key.substring(4));
                            levelNumbers.add(level);
                        } catch (NumberFormatException e) {
                            // 忽略无效的星级配置
                        }
                    }
                }

                // 排序并添加到下拉框
                java.util.Collections.sort(levelNumbers);
                for (Integer level : levelNumbers) {
                    this.effectStarLevelComboBox.addItem(level + "星");
                }
            } else {
                // 如果配置文件不可用，使用默认星级
                String[] defaultLevels = { "6", "9", "12", "15", "18", "19" };
                for (String level : defaultLevels) {
                    this.effectStarLevelComboBox.addItem(level + "星");
                }
            }
        } catch (Exception e) {
            // 出错时使用默认星级
            String[] defaultLevels = { "6", "9", "12", "15", "18", "19" };
            for (String level : defaultLevels) {
                this.effectStarLevelComboBox.addItem(level + "星");
            }
        }

        // 初始化特效类型下拉框
        this.availableEffectTypeComboBox.removeAllItems();
        String[] effectTypes = {
                // 淬炼5.6版本经典特效
                "flame_basic - 基础火焰", "footstep - 脚步", "spiral_flame - 螺旋火焰",
                "rotating_halo - 旋转光环", "four_direction_spell - 四方向法术", "wings_56 - 5.6版本翅膀",
                // 现代版本基础特效
                "wings - 翅膀", "halo - 光环", "flame - 火焰", "nebula - 星云", "tornado - 龙卷风", "stars - 星星",
                // 现代版本几何特效
                "spiral - 螺旋", "sphere - 球体", "orbit - 轨道", "cube - 立方体", "helix - 螺旋桨",
                // 现代版本动态特效
                "batman - 蝙蝠侠", "popper - 爆炸螺旋", "pulse - 脉冲", "whirl - 旋涡", "whirlwind - 旋风", "invocation - 召唤法阵",
                // 现代版本高级特效
                "lightning - 雷电", "rainbow - 彩虹", "spacerift - 时空裂缝", "frost - 冰霜", "shadow - 暗影",
                // PlayerParticles集成特效
                "pp_wings - PP翅膀", "pp_beam - PP光束", "pp_quadhelix - PP四重螺旋", "pp_swords - PP剑击"
        };

        for (String effectType : effectTypes) {
            this.availableEffectTypeComboBox.addItem(effectType);
        }

        // 初始化颜色下拉框
        String[] colors = { "红", "橙", "黄", "绿", "蓝", "紫", "粉", "黑", "白", "灰", "银" };

        this.effectColor1ComboBox.removeAllItems();
        this.effectColor2ComboBox.removeAllItems();
        this.effectColor3ComboBox.removeAllItems();

        for (String color : colors) {
            this.effectColor1ComboBox.addItem(color);
            this.effectColor2ComboBox.addItem(color);
            this.effectColor3ComboBox.addItem(color);
        }

        // 设置默认颜色
        this.effectColor1ComboBox.setSelectedItem("红");
        this.effectColor2ComboBox.setSelectedItem("橙");
        this.effectColor3ComboBox.setSelectedItem("黄");
    }

    /**
     * 加载选中星级的特效列表
     */
    private void loadEffectsForSelectedLevel() {
        String selectedLevel = (String) this.effectStarLevelComboBox.getSelectedItem();
        if (selectedLevel == null || selectedLevel.equals("选择星级")) {
            this.currentEffectsModel.clear();
            return;
        }

        // 提取星级数字
        String levelStr = selectedLevel.replace("星", "");

        this.currentEffectsModel.clear();

        try {
            // 从config.yml读取特效配置
            if (Cuilian.config != null) {
                String configPath = "eff.leve" + levelStr + ".effects";
                if (Cuilian.config.isList(configPath)) {
                    java.util.List<?> effectsList = Cuilian.config.getList(configPath);
                    if (effectsList != null) {
                        for (Object effectObj : effectsList) {
                            if (effectObj instanceof java.util.Map) {
                                @SuppressWarnings("unchecked")
                                java.util.Map<String, Object> effectMap = (java.util.Map<String, Object>) effectObj;
                                String type = (String) effectMap.get("type");
                                boolean enabled = (Boolean) effectMap.getOrDefault("enabled", true);
                                String color1 = (String) effectMap.getOrDefault("colore1", "红");
                                String color2 = (String) effectMap.getOrDefault("colore2", "橙");
                                String color3 = (String) effectMap.getOrDefault("colore3", "黄");

                                // 获取中文描述
                                String typeDisplay = getEffectTypeDisplayName(type);

                                String displayText = String.format("%s [%s] (%s,%s,%s)",
                                        typeDisplay, enabled ? "启用" : "禁用", color1, color2, color3);
                                this.currentEffectsModel.addElement(displayText);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "加载特效配置时出现错误: " + e.getMessage());
        }
    }

    /**
     * 添加特效到当前星级
     */
    private void addEffectToCurrentLevel() {
        String selectedLevel = (String) this.effectStarLevelComboBox.getSelectedItem();
        if (selectedLevel == null || selectedLevel.equals("选择星级")) {
            JOptionPane.showMessageDialog(null, "请先选择星级！");
            return;
        }

        String effectTypeDisplay = (String) this.availableEffectTypeComboBox.getSelectedItem();
        if (effectTypeDisplay == null) {
            JOptionPane.showMessageDialog(null, "请选择特效类型！");
            return;
        }

        boolean enabled = this.effectEnabledCheckBox.isSelected();
        String color1 = (String) this.effectColor1ComboBox.getSelectedItem();
        String color2 = (String) this.effectColor2ComboBox.getSelectedItem();
        String color3 = (String) this.effectColor3ComboBox.getSelectedItem();

        // 添加到显示列表（显示中文描述）
        String displayText = String.format("%s [%s] (%s,%s,%s)",
                effectTypeDisplay, enabled ? "启用" : "禁用", color1, color2, color3);
        this.currentEffectsModel.addElement(displayText);

        // 自动保存配置并重新加载
        saveEffectConfigurationSilently();

        // 更新状态标签
        updateStatusLabel("特效已添加并自动保存: " + effectTypeDisplay.split(" - ")[1], true);
        System.out.println("特效已添加并自动保存: " + effectTypeDisplay);
    }

    /**
     * 加载选中的特效到编辑区域
     */
    private void loadSelectedEffectForEditing() {
        int selectedIndex = this.currentEffectsList.getSelectedIndex();
        if (selectedIndex == -1) {
            return;
        }

        String effectText = this.currentEffectsModel.getElementAt(selectedIndex);

        try {
            // 解析特效文本: "type - 中文 [启用/禁用] (color1,color2,color3)"
            String[] parts = effectText.split(" \\[");
            if (parts.length >= 2) {
                String typeDisplay = parts[0];
                String[] statusAndColors = parts[1].split("\\] \\(");
                if (statusAndColors.length >= 2) {
                    boolean enabled = statusAndColors[0].equals("启用");
                    String colorsStr = statusAndColors[1].replace(")", "");
                    String[] colors = colorsStr.split(",");

                    // 设置编辑区域的值（使用完整的显示名称）
                    this.availableEffectTypeComboBox.setSelectedItem(typeDisplay);
                    this.effectEnabledCheckBox.setSelected(enabled);

                    if (colors.length > 0)
                        this.effectColor1ComboBox.setSelectedItem(colors[0]);
                    if (colors.length > 1)
                        this.effectColor2ComboBox.setSelectedItem(colors[1]);
                    if (colors.length > 2)
                        this.effectColor3ComboBox.setSelectedItem(colors[2]);

                    // 更新状态标签
                    updateStatusLabel("特效已加载到编辑区域: " + typeDisplay.split(" - ")[1], true);
                    System.out.println("特效已加载到编辑区域: " + typeDisplay);
                }
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "解析特效数据时出现错误: " + e.getMessage());
        }
    }

    /**
     * 删除选中的特效
     */
    private void removeSelectedEffect() {
        int selectedIndex = this.currentEffectsList.getSelectedIndex();
        if (selectedIndex == -1) {
            JOptionPane.showMessageDialog(null, "请先选择要删除的特效！");
            return;
        }

        this.currentEffectsModel.remove(selectedIndex);

        // 自动保存配置并重新加载
        saveEffectConfigurationSilently();

        // 更新状态标签
        updateStatusLabel("特效已删除并自动保存", true);
        System.out.println("特效已删除并自动保存");
    }

    /**
     * 静默保存特效配置（不显示消息框）
     */
    private void saveEffectConfigurationSilently() {
        String selectedLevel = (String) this.effectStarLevelComboBox.getSelectedItem();
        if (selectedLevel == null || selectedLevel.equals("选择星级")) {
            return;
        }

        String levelStr = selectedLevel.replace("星", "");

        try {
            // 构建特效列表
            java.util.List<java.util.Map<String, Object>> effectsList = new java.util.ArrayList<>();

            for (int i = 0; i < this.currentEffectsModel.size(); i++) {
                String effectText = this.currentEffectsModel.getElementAt(i);

                // 解析特效文本: "type - 中文 [启用/禁用] (color1,color2,color3)"
                String[] parts = effectText.split(" \\[");
                if (parts.length >= 2) {
                    String typeDisplay = parts[0];
                    // 提取英文部分作为实际的特效类型
                    String type = typeDisplay.split(" - ")[0];

                    String[] statusAndColors = parts[1].split("\\] \\(");
                    if (statusAndColors.length >= 2) {
                        boolean enabled = statusAndColors[0].equals("启用");
                        String colorsStr = statusAndColors[1].replace(")", "");
                        String[] colors = colorsStr.split(",");

                        java.util.Map<String, Object> effectMap = new java.util.HashMap<>();
                        effectMap.put("type", type);
                        effectMap.put("enabled", enabled);
                        effectMap.put("colore1", colors.length > 0 ? colors[0] : "红");
                        effectMap.put("colore2", colors.length > 1 ? colors[1] : "橙");
                        effectMap.put("colore3", colors.length > 2 ? colors[2] : "黄");

                        effectsList.add(effectMap);
                    }
                }
            }

            // 保存到配置文件
            String configPath = "eff.leve" + levelStr + ".effects";
            Cuilian.config.set(configPath, effectsList);

            // 保存文件
            Cuilian.config.save(Cuilian.filess);

            // 重新加载配置到游戏中
            reloadEffectConfiguration();

        } catch (Exception e) {
            System.out.println("静默保存特效配置时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 初始化符咒数据
     */
    private void initializeCharmData() {
        // 初始化符咒等级下拉框
        this.charmLevelComboBox.removeAllItems();

        // 动态获取最大配置星级
        try {
            int maxLevel = cn.winde.cuilian.Cuilian.getMaxConfiguredLevel();
            for (int i = 1; i <= maxLevel; i++) {
                this.charmLevelComboBox.addItem(i + "星");
            }
        } catch (Exception e) {
            // 如果出错，使用默认星级
            for (int i = 1; i <= 20; i++) {
                this.charmLevelComboBox.addItem(i + "星");
            }
        }

        // 初始化符咒在线玩家网格面板
        refreshCharmOnlinePlayers();
    }

    /**
     * 初始化符咒在线玩家列表
     */
    private void initializeCharmOnlinePlayersList() {
        this.charmOnlinePlayersModel.clear();

        // 使用延迟任务确保服务器完全启动后再加载玩家列表
        javax.swing.SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                try {
                    // 获取当前在线玩家列表
                    for (org.bukkit.entity.Player player : org.bukkit.Bukkit.getOnlinePlayers()) {
                        charmOnlinePlayersModel.addElement(player.getName());
                    }
                } catch (Exception e) {
                }
            }
        });
    }

    /**
     * 刷新符咒管理的在线玩家网格面板
     */
    private void refreshCharmOnlinePlayers() {
        if (this.charmPlayersGridPanel == null) {
            return;
        }

        java.util.List<String> onlinePlayers = new java.util.ArrayList<>();

        // 获取在线玩家
        try {
            if (org.bukkit.Bukkit.getServer() != null) {
                for (org.bukkit.entity.Player player : org.bukkit.Bukkit.getOnlinePlayers()) {
                    onlinePlayers.add(player.getName());
                }
            }
        } catch (Exception e) {
            // 如果获取失败，添加一些测试数据
            onlinePlayers.add("测试玩家1");
            onlinePlayers.add("测试玩家2");
            onlinePlayers.add("测试玩家3");
        }

        // 更新玩家网格面板
        this.charmPlayersGridPanel.updatePlayers(onlinePlayers);
    }

    /**
     * 刷新符咒在线玩家列表 (兼容旧方法)
     */
    private void refreshCharmOnlinePlayersList() {
        this.charmOnlinePlayersModel.clear();

        try {
            // 获取在线玩家列表
            for (org.bukkit.entity.Player player : org.bukkit.Bukkit.getOnlinePlayers()) {
                this.charmOnlinePlayersModel.addElement(player.getName());
            }
        } catch (Exception e) {
        }
    }

    /**
     * 给予符咒给玩家
     */
    private void giveCharmToPlayer() {
        String charmType = (String) this.charmTypeComboBox.getSelectedItem();
        String levelStr = (String) this.charmLevelComboBox.getSelectedItem();
        String quantityStr = this.charmQuantityField.getText();

        // 优先从网格面板获取选中的玩家，如果没有选中则从输入框获取
        String targetPlayer = null;
        if (this.charmPlayersGridPanel != null && this.charmPlayersGridPanel.getSelectedPlayer() != null) {
            targetPlayer = this.charmPlayersGridPanel.getSelectedPlayer();
        } else {
            targetPlayer = this.charmTargetPlayerField.getText();
        }

        if (charmType == null || levelStr == null) {
            JOptionPane.showMessageDialog(null, "请选择符咒类型和等级！");
            return;
        }

        if (targetPlayer == null || targetPlayer.trim().isEmpty()) {
            JOptionPane.showMessageDialog(null, "请选择在线玩家或输入离线玩家名！");
            return;
        }

        try {
            int level = Integer.parseInt(levelStr.replace("星", ""));
            int quantity = Integer.parseInt(quantityStr);

            if (quantity <= 0) {
                JOptionPane.showMessageDialog(null, "数量必须大于0！");
                return;
            }

            // 创建符咒物品
            ItemStack item;
            if (charmType.equals("淬炼符咒")) {
                item = cn.winde.cuilian.Cuilian.createCuilianCharm(level);
            } else {
                item = cn.winde.cuilian.Cuilian.createCuilianDirectRod(level);
            }

            if (item == null) {
                JOptionPane.showMessageDialog(null, "创建符咒失败！");
                return;
            }

            item.setAmount(quantity);

            // 检查玩家是否在线
            Player onlinePlayer = org.bukkit.Bukkit.getPlayer(targetPlayer.trim());
            if (onlinePlayer != null && onlinePlayer.isOnline()) {
                // 在线玩家：直接给予物品
                onlinePlayer.getInventory().addItem(item);
                onlinePlayer.sendMessage("§a你获得了 §6" + quantity + "个 " + level + "星" + charmType);

                JOptionPane.showMessageDialog(null,
                        "成功给予在线玩家 " + targetPlayer.trim() + " " + quantity + "个 " + level + "星" + charmType);
            } else {
                // 离线玩家：使用命令方式给予
                try {
                    // 检查玩家是否存在于服务器
                    @SuppressWarnings("deprecation")
                    OfflinePlayer offlinePlayer = org.bukkit.Bukkit.getOfflinePlayer(targetPlayer.trim());

                    if (offlinePlayer != null && offlinePlayer.hasPlayedBefore()) {
                        // 使用控制台命令给予离线玩家物品
                        String command = String.format("cuilian give %s %s %d %d",
                                targetPlayer.trim(),
                                charmType.equals("淬炼符咒") ? "charm" : "directrod",
                                level,
                                quantity);

                        // 在主线程中执行命令
                        org.bukkit.Bukkit.getScheduler().runTask(cn.winde.cuilian.Cuilian.getInstance(), () -> {
                            org.bukkit.Bukkit.getServer().dispatchCommand(
                                    org.bukkit.Bukkit.getConsoleSender(),
                                    command);
                        });

                        JOptionPane.showMessageDialog(null,
                                "成功给予离线玩家 " + targetPlayer.trim() + " " + quantity + "个 " + level + "星" + charmType +
                                        "\n物品将在玩家下次上线时发送到背包");
                    } else {
                        JOptionPane.showMessageDialog(null,
                                "玩家 " + targetPlayer.trim() + " 从未进入过服务器！\n请确认玩家名称正确。");
                    }
                } catch (Exception ex) {
                    // 如果命令执行失败，提示用户手动操作
                    JOptionPane.showMessageDialog(null,
                            "自动给予离线玩家失败！\n" +
                                    "请在游戏中手动执行以下命令：\n" +
                                    "/cuilian give " + targetPlayer.trim() + " " +
                                    (charmType.equals("淬炼符咒") ? "charm" : "directrod") + " " +
                                    level + " " + quantity);
                }
            }

        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "等级和数量必须是有效数字！");
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "给予符咒时出现错误: " + e.getMessage());
        }
    }

    /**
     * 静态方法：处理玩家加入事件
     * 自动更新UI中的在线玩家列表
     */
    public static void onPlayerJoin(String playerName) {
        if (instance != null) {
            // 使用SwingUtilities确保在EDT线程中执行UI更新
            javax.swing.SwingUtilities.invokeLater(new Runnable() {
                @Override
                public void run() {
                    try {
                        // 更新符咒管理的在线玩家列表
                        if (instance.charmOnlinePlayersModel != null) {
                            // 检查玩家是否已经在列表中
                            boolean playerExists = false;
                            for (int i = 0; i < instance.charmOnlinePlayersModel.getSize(); i++) {
                                if (playerName.equals(instance.charmOnlinePlayersModel.getElementAt(i))) {
                                    playerExists = true;
                                    break;
                                }
                            }

                            // 如果玩家不在列表中，添加到列表
                            if (!playerExists) {
                                instance.charmOnlinePlayersModel.addElement(playerName);
                            }
                        }

                        // 直接向网格面板添加玩家，而不是重新刷新整个列表
                        if (instance.onlinePlayersGridPanel != null) {
                            instance.onlinePlayersGridPanel.addPlayer(playerName);
                        }

                        // 同时更新符咒管理的网格面板
                        if (instance.charmPlayersGridPanel != null) {
                            instance.charmPlayersGridPanel.addPlayer(playerName);
                        }

                        // 同时更新游戏内GUI面板的玩家列表
                        if (instance.guiPlayersGridPanel != null) {
                            instance.guiPlayersGridPanel.addPlayer(playerName);
                        }

                    } catch (Exception e) {
                        // 忽略错误
                    }
                }
            });
        }
    }

    /**
     * 静态方法：处理玩家退出事件
     * 自动更新UI中的在线玩家列表
     */
    public static void onPlayerLeave(String playerName) {
        if (instance != null) {
            // 使用SwingUtilities确保在EDT线程中执行UI更新
            javax.swing.SwingUtilities.invokeLater(new Runnable() {
                @Override
                public void run() {
                    try {
                        // 更新符咒管理的在线玩家列表
                        if (instance.charmOnlinePlayersModel != null) {
                            instance.charmOnlinePlayersModel.removeElement(playerName);
                        }

                        // 如果当前输入框中是该玩家，清空输入框
                        if (instance.charmTargetPlayerField != null &&
                                playerName.equals(instance.charmTargetPlayerField.getText())) {
                            instance.charmTargetPlayerField.setText("");
                        }

                        // 如果离线玩家输入框中是该玩家，清空输入框
                        if (instance.offlinePlayerField != null &&
                                playerName.equals(instance.offlinePlayerField.getText())) {
                            instance.offlinePlayerField.setText("");
                        }

                        // 直接从网格面板移除玩家，而不是重新刷新整个列表
                        if (instance.onlinePlayersGridPanel != null) {
                            instance.onlinePlayersGridPanel.removePlayer(playerName);
                        }

                        // 同时从符咒管理的网格面板移除玩家
                        if (instance.charmPlayersGridPanel != null) {
                            instance.charmPlayersGridPanel.removePlayer(playerName);
                        }

                        // 同时从游戏内GUI面板移除玩家
                        if (instance.guiPlayersGridPanel != null) {
                            instance.guiPlayersGridPanel.removePlayer(playerName);
                        }

                        // 如果游戏内GUI输入框中是该玩家，清空输入框
                        if (instance.guiPlayerNameField != null &&
                                playerName.equals(instance.guiPlayerNameField.getText())) {
                            instance.guiPlayerNameField.setText("");
                        }

                    } catch (Exception e) {
                        // 忽略错误
                    }
                }
            });
        }
    }

    /**
     * 创建标题面板
     */
    private JPanel createTitlePanel() {
        JPanel titlePanel = new JPanel(new java.awt.FlowLayout(java.awt.FlowLayout.CENTER));
        titlePanel.setBackground(java.awt.Color.WHITE);
        titlePanel.setBorder(BorderFactory.createEmptyBorder(10, 0, 10, 0));

        JLabel titleLabel = new JLabel("淬炼插件管理界面 v2.0");
        titleLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 18));
        titleLabel.setForeground(new java.awt.Color(51, 102, 153));
        titlePanel.add(titleLabel);

        return titlePanel;
    }

    /**
     * 创建玩家管理面板
     */
    private JPanel createPlayerManagementPanel() {
        JPanel panel = new JPanel(new java.awt.BorderLayout());
        panel.setBackground(java.awt.Color.WHITE);

        // 创建可调节大小的分割面板
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setDividerLocation(400); // 初始分割位置
        splitPane.setResizeWeight(0.4); // 左侧占40%权重
        splitPane.setBorder(null);
        splitPane.setBackground(java.awt.Color.WHITE);

        // 左侧面板 - 在线玩家列表
        JPanel leftPanel = createPlayerLeftPanel();
        splitPane.setLeftComponent(leftPanel);

        // 右侧面板 - 玩家物品管理
        JPanel rightPanel = createPlayerRightPanel();
        splitPane.setRightComponent(rightPanel);

        panel.add(splitPane, java.awt.BorderLayout.CENTER);

        // 初始化在线玩家列表
        refreshOnlinePlayers();

        return panel;
    }

    /**
     * 创建玩家管理左侧面板 - 在线玩家列表
     */
    private JPanel createPlayerLeftPanel() {
        JPanel leftPanel = new JPanel(new java.awt.BorderLayout());
        leftPanel.setBackground(java.awt.Color.WHITE);
        leftPanel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createEtchedBorder(), "在线玩家列表",
                javax.swing.border.TitledBorder.DEFAULT_JUSTIFICATION,
                javax.swing.border.TitledBorder.DEFAULT_POSITION,
                new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14)));

        // 创建多列玩家网格面板
        this.onlinePlayersGridPanel = new PlayerGridPanel();
        JScrollPane scrollPane = new JScrollPane(this.onlinePlayersGridPanel);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_NEVER);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setPreferredSize(new java.awt.Dimension(350, 450));
        leftPanel.add(scrollPane, java.awt.BorderLayout.CENTER);

        // 底部操作按钮
        JPanel buttonPanel = new JPanel(new java.awt.FlowLayout(java.awt.FlowLayout.CENTER, 10, 5));
        buttonPanel.setBackground(java.awt.Color.WHITE);

        JButton refreshBtn = new JButton("刷新在线玩家");
        refreshBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        refreshBtn.setPreferredSize(new java.awt.Dimension(120, 30));
        refreshBtn.addActionListener(e -> refreshOnlinePlayers());
        buttonPanel.add(refreshBtn);

        JButton clearBtn = new JButton("清除选择");
        clearBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        clearBtn.setPreferredSize(new java.awt.Dimension(100, 30));
        clearBtn.addActionListener(e -> {
            if (onlinePlayersGridPanel != null) {
                onlinePlayersGridPanel.clearSelection();
            }
            if (offlinePlayerField != null) {
                offlinePlayerField.setText("");
            }
        });
        buttonPanel.add(clearBtn);

        leftPanel.add(buttonPanel, java.awt.BorderLayout.SOUTH);

        return leftPanel;
    }

    /**
     * 创建玩家管理右侧面板 - 玩家物品管理
     */
    private JPanel createPlayerRightPanel() {
        JPanel rightPanel = new JPanel(new java.awt.GridBagLayout());
        rightPanel.setBackground(java.awt.Color.WHITE);
        rightPanel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createEtchedBorder(), "玩家物品管理",
                javax.swing.border.TitledBorder.DEFAULT_JUSTIFICATION,
                javax.swing.border.TitledBorder.DEFAULT_POSITION,
                new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14)));

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 第一行：离线玩家名称
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        JLabel offlineLabel = new JLabel("离线玩家名称:");
        offlineLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        rightPanel.add(offlineLabel, gbc);

        gbc.gridx = 1;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        this.offlinePlayerField = new JTextField();
        this.offlinePlayerField.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
        this.offlinePlayerField.setPreferredSize(new java.awt.Dimension(300, 30));
        this.offlinePlayerField.addKeyListener(new java.awt.event.KeyAdapter() {
            @Override
            public void keyTyped(java.awt.event.KeyEvent e) {
                if (!offlinePlayerField.getText().trim().isEmpty()) {
                    if (onlinePlayersGridPanel != null) {
                        onlinePlayersGridPanel.clearSelection();
                    }
                }
            }
        });
        rightPanel.add(this.offlinePlayerField, gbc);

        // 第二行：套装管理
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        JLabel suitLabel = new JLabel("套装类型:");
        suitLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        rightPanel.add(suitLabel, gbc);

        gbc.gridx = 1;
        gbc.gridwidth = 1;
        gbc.weightx = 1.0;
        this.suitComboBox = new JComboBox<String>();
        this.suitComboBox.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
        this.suitComboBox.setPreferredSize(new java.awt.Dimension(200, 30));
        loadAvailableSuits();
        rightPanel.add(this.suitComboBox, gbc);

        gbc.gridx = 2;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        JButton sendSuitBtn = new JButton("发送套装");
        sendSuitBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
        sendSuitBtn.setPreferredSize(new java.awt.Dimension(100, 30));
        sendSuitBtn.addActionListener(e -> sendSuitToPlayer());
        rightPanel.add(sendSuitBtn, gbc);

        // 第三行：淬炼石类型
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        JLabel stoneTypeLabel = new JLabel("淬炼石类型:");
        stoneTypeLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        rightPanel.add(stoneTypeLabel, gbc);

        gbc.gridx = 1;
        gbc.gridwidth = 1;
        gbc.weightx = 1.0;
        this.stoneComboBox = new JComboBox<String>();
        this.stoneComboBox
                .setModel(new DefaultComboBoxModel<String>(new String[] { "普通", "中等", "高等", "上等", "符咒", "吞噬" }));
        this.stoneComboBox.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
        this.stoneComboBox.setPreferredSize(new java.awt.Dimension(200, 30));
        rightPanel.add(this.stoneComboBox, gbc);

        gbc.gridx = 2;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        JButton sendStoneBtn = new JButton("发送淬炼石");
        sendStoneBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
        sendStoneBtn.setPreferredSize(new java.awt.Dimension(100, 30));
        sendStoneBtn.addActionListener(e -> sendStoneToPlayer());
        rightPanel.add(sendStoneBtn, gbc);

        // 第四行：数量设置
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        // 减少上边距，让数量标签更靠近淬炼石类型
        gbc.insets = new java.awt.Insets(2, 8, 8, 8);
        JLabel amountLabel = new JLabel("数量:");
        amountLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        rightPanel.add(amountLabel, gbc);

        gbc.gridx = 1;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        this.stoneAmountField = new JTextField("1");
        this.stoneAmountField.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
        this.stoneAmountField.setPreferredSize(new java.awt.Dimension(300, 30));
        rightPanel.add(this.stoneAmountField, gbc);

        return rightPanel;
    }

    /**
     * 创建套装创建面板
     */
    private JPanel createSuitCreatorPanel() {
        JPanel mainPanel = new JPanel(new java.awt.BorderLayout());
        mainPanel.setBackground(java.awt.Color.WHITE);

        // 创建左右分栏面板
        JPanel splitPanel = new JPanel(new java.awt.BorderLayout());
        splitPanel.setBackground(java.awt.Color.WHITE);
        splitPanel.setBorder(BorderFactory.createEmptyBorder(15, 15, 15, 15));

        // 左侧面板 - 基本信息和属性配置
        JPanel leftPanel = createLeftConfigPanel();

        // 右侧面板 - 描述和特效配置
        JPanel rightPanel = createRightConfigPanel();

        // 添加到分栏面板
        splitPanel.add(leftPanel, java.awt.BorderLayout.WEST);
        splitPanel.add(rightPanel, java.awt.BorderLayout.CENTER);

        // 添加滚动支持
        JScrollPane scrollPane = new JScrollPane(splitPanel);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);

        mainPanel.add(scrollPane, java.awt.BorderLayout.CENTER);

        // 初始化删除套装列表
        loadDeleteSuitList();

        return mainPanel;
    }

    /**
     * 创建左侧配置面板
     */
    private JPanel createLeftConfigPanel() {
        JPanel leftPanel = new JPanel(new java.awt.GridBagLayout());
        leftPanel.setBackground(java.awt.Color.WHITE);
        leftPanel.setBorder(BorderFactory.createTitledBorder("基本配置"));
        leftPanel.setPreferredSize(new java.awt.Dimension(500, 600));

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 套装基本信息
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        JLabel basicInfoLabel = new JLabel("套装基本信息");
        basicInfoLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        leftPanel.add(basicInfoLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("套装名称:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.suitNameField = new JTextField(20);
        leftPanel.add(this.suitNameField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("品质等级:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 0.5;
        this.qualityComboBox = new JComboBox<String>();
        this.qualityComboBox.setModel(new DefaultComboBoxModel<String>(new String[] { "1", "2", "3", "4", "5", "6" }));
        this.qualityComboBox.setSelectedIndex(5);
        leftPanel.add(this.qualityComboBox, gbc);

        // 装备名称配置
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        JLabel equipLabel = new JLabel("装备名称配置");
        equipLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        leftPanel.add(equipLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("头盔:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.headNameField = new JTextField(15);
        leftPanel.add(this.headNameField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("胸甲:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.chestNameField = new JTextField(15);
        leftPanel.add(this.chestNameField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("护腿:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.legNameField = new JTextField(15);
        leftPanel.add(this.legNameField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("靴子:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.footNameField = new JTextField(15);
        leftPanel.add(this.footNameField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("剑:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.swordNameField = new JTextField(15);
        leftPanel.add(this.swordNameField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("弓:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.bowNameField = new JTextField(15);
        leftPanel.add(this.bowNameField, gbc);

        // 属性配置
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        JLabel attributeLabel = new JLabel("属性配置");
        attributeLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        leftPanel.add(attributeLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("攻击:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.attackField = new JTextField("15", 12);
        leftPanel.add(this.attackField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("防御:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.defenseField = new JTextField("20", 12);
        leftPanel.add(this.defenseField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("生命:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.healthField = new JTextField("40", 12);
        leftPanel.add(this.healthField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("速度:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.speedField = new JTextField("0.1", 12);
        leftPanel.add(this.speedField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("跳跃:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.jumpField = new JTextField("1", 12);
        leftPanel.add(this.jumpField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("吸血:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.vampireField = new JTextField("10", 12);
        leftPanel.add(this.vampireField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        this.infiniteDurabilityCheckBox = new JCheckBox("无限耐久");
        this.infiniteDurabilityCheckBox.setToolTipText("启用后套装装备将拥有无限耐久");
        leftPanel.add(this.infiniteDurabilityCheckBox, gbc);

        // 宝石等级配置
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        JLabel gemLabel = new JLabel("宝石等级配置");
        gemLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        leftPanel.add(gemLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("头盔宝石:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.headGemField = new JTextField("15", 12);
        leftPanel.add(this.headGemField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("胸甲宝石:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.chestGemField = new JTextField("15", 12);
        leftPanel.add(this.chestGemField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("护腿宝石:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.legGemField = new JTextField("15", 12);
        leftPanel.add(this.legGemField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("靴子宝石:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.footGemField = new JTextField("15", 12);
        leftPanel.add(this.footGemField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("剑宝石:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.swordGemField = new JTextField("18", 12);
        leftPanel.add(this.swordGemField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        leftPanel.add(new JLabel("弓宝石:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.bowGemField = new JTextField("16", 12);
        leftPanel.add(this.bowGemField, gbc);

        return leftPanel;
    }

    /**
     * 创建右侧配置面板
     */
    private JPanel createRightConfigPanel() {
        JPanel rightPanel = new JPanel(new java.awt.GridBagLayout());
        rightPanel.setBackground(java.awt.Color.WHITE);
        rightPanel.setBorder(BorderFactory.createTitledBorder("描述和特效配置"));
        rightPanel.setPreferredSize(new java.awt.Dimension(450, 600));

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 描述配置
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.weightx = 1.0;
        JLabel descLabel = new JLabel("套装描述");
        descLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        rightPanel.add(descLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.fill = java.awt.GridBagConstraints.BOTH;
        gbc.weightx = 1.0;
        gbc.weighty = 0.3;
        this.descriptionArea = new javax.swing.JTextArea();
        this.descriptionArea.setText("§6§l自定义套装\n§e传说中的装者套装\n§a拥有强大的力量和神秘的特效");
        this.descriptionArea.setLineWrap(true);
        this.descriptionArea.setWrapStyleWord(true);
        this.descriptionArea.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        JScrollPane descScrollPane = new JScrollPane(this.descriptionArea);
        descScrollPane.setPreferredSize(new java.awt.Dimension(400, 100));
        rightPanel.add(descScrollPane, gbc);

        // 特效配置
        row++;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        gbc.weighty = 0;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        JLabel effectLabel = new JLabel("特效配置");
        effectLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        rightPanel.add(effectLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        rightPanel.add(new JLabel("特效类型:"), gbc);
        gbc.gridx = 1;
        gbc.gridwidth = 3;
        gbc.weightx = 1.0;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        this.effectTypeComboBox = new JComboBox<String>();
        this.effectTypeComboBox.setModel(new DefaultComboBoxModel<String>(new String[] {
                "flame_basic - 基础火焰", "footstep - 脚步", "spiral_flame - 螺旋火焰",
                "rotating_halo - 旋转光环", "four_direction_spell - 四方向法术", "wings_56 - 5.6版本翅膀",
                "wings - 翅膀", "halo - 光环", "flame - 火焰", "nebula - 星云", "tornado - 龙卷风", "stars - 星星",
                "spiral - 螺旋", "sphere - 球体", "orbit - 轨道", "cube - 立方体", "helix - 螺旋桨",
                "batman - 蝙蝠侠", "popper - 爆炸螺旋", "pulse - 脉冲", "whirl - 旋涡", "whirlwind - 旋风", "invocation - 召唤法阵",
                "lightning - 雷电", "rainbow - 彩虹", "spacerift - 时空裂缝", "frost - 冰霜", "shadow - 暗影"
        }));
        rightPanel.add(this.effectTypeComboBox, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        rightPanel.add(new JLabel("颜色1:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.color1ComboBox = new JComboBox<String>();
        this.color1ComboBox.setModel(new DefaultComboBoxModel<String>(new String[] {
                "红", "橙", "黄", "绿", "蓝", "紫", "粉", "黑", "白", "灰", "银"
        }));
        rightPanel.add(this.color1ComboBox, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        rightPanel.add(new JLabel("颜色2:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.color2ComboBox = new JComboBox<String>();
        this.color2ComboBox.setModel(new DefaultComboBoxModel<String>(new String[] {
                "红", "橙", "黄", "绿", "蓝", "紫", "粉", "黑", "白", "灰", "银"
        }));
        this.color2ComboBox.setSelectedIndex(1); // 默认橙色
        rightPanel.add(this.color2ComboBox, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        rightPanel.add(new JLabel("颜色3:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.color3ComboBox = new JComboBox<String>();
        this.color3ComboBox.setModel(new DefaultComboBoxModel<String>(new String[] {
                "红", "橙", "黄", "绿", "蓝", "紫", "粉", "黑", "白", "灰", "银"
        }));
        this.color3ComboBox.setSelectedIndex(4); // 默认蓝色
        rightPanel.add(this.color3ComboBox, gbc);

        // 特效列表
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.weightx = 1.0;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        gbc.weighty = 0;
        JLabel effectsListLabel = new JLabel("已添加特效:");
        effectsListLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        rightPanel.add(effectsListLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.fill = java.awt.GridBagConstraints.BOTH;
        gbc.weightx = 1.0;
        gbc.weighty = 0.3;
        this.effectsModel = new DefaultListModel<String>();
        this.effectsList = new JList<String>(this.effectsModel);
        this.effectsList.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 11));
        JScrollPane effectsScrollPane = new JScrollPane(this.effectsList);
        effectsScrollPane.setPreferredSize(new java.awt.Dimension(400, 100));
        rightPanel.add(effectsScrollPane, gbc);

        // 特效操作按钮
        row++;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        gbc.weighty = 0;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        JButton addEffectBtn = new JButton("添加特效");
        addEffectBtn.addActionListener(e -> addEffectToList());
        rightPanel.add(addEffectBtn, gbc);

        gbc.gridx = 1;
        JButton removeEffectBtn = new JButton("删除特效");
        removeEffectBtn.addActionListener(e -> removeEffectFromList());
        rightPanel.add(removeEffectBtn, gbc);

        gbc.gridx = 2;
        gbc.gridwidth = 2;
        JButton clearEffectsBtn = new JButton("清空特效");
        clearEffectsBtn.addActionListener(e -> effectsModel.clear());
        rightPanel.add(clearEffectsBtn, gbc);

        // 套装管理按钮
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JButton saveSuitBtn = new JButton("保存套装到Suit.yml");
        saveSuitBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        saveSuitBtn.addActionListener(e -> saveSuitToConfig());
        rightPanel.add(saveSuitBtn, gbc);

        gbc.gridx = 2;
        gbc.gridwidth = 2;
        JButton clearFormBtn = new JButton("清空表单");
        clearFormBtn.addActionListener(e -> clearSuitForm());
        rightPanel.add(clearFormBtn, gbc);

        // 套装删除区域
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.fill = java.awt.GridBagConstraints.NONE;
        JLabel deleteSuitLabel = new JLabel("删除套装:");
        deleteSuitLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        rightPanel.add(deleteSuitLabel, gbc);

        gbc.gridx = 1;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        this.deleteSuitComboBox = new JComboBox<String>();
        rightPanel.add(this.deleteSuitComboBox, gbc);

        gbc.gridx = 3;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.fill = java.awt.GridBagConstraints.NONE;
        JButton deleteSuitBtn = new JButton("删除套装");
        deleteSuitBtn.addActionListener(e -> deleteSuitFromConfig());
        rightPanel.add(deleteSuitBtn, gbc);

        return rightPanel;
    }

    /**
     * 创建星级配置面板
     */
    private JPanel createStarConfigPanel() {
        JPanel mainPanel = new JPanel(new java.awt.BorderLayout());
        mainPanel.setBackground(java.awt.Color.WHITE);

        // 创建左右分栏布局
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setDividerLocation(500);
        splitPane.setResizeWeight(0.5);
        splitPane.setBorder(null);

        // 左侧面板 - 基本配置
        JPanel leftPanel = createStarLeftPanel();
        splitPane.setLeftComponent(leftPanel);

        // 右侧面板 - 强化石配置
        JPanel rightPanel = createStarRightPanel();
        splitPane.setRightComponent(rightPanel);

        mainPanel.add(splitPane, java.awt.BorderLayout.CENTER);

        // 初始化现有星级列表
        loadExistingStarLevels();

        return mainPanel;
    }

    /**
     * 创建星级配置左侧面板
     */
    private JPanel createStarLeftPanel() {
        JPanel contentPanel = new JPanel(new java.awt.GridBagLayout());
        contentPanel.setBackground(java.awt.Color.WHITE);
        contentPanel.setBorder(BorderFactory.createTitledBorder("星级基本配置"));
        contentPanel.setPreferredSize(new java.awt.Dimension(480, 500));

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 星级基本信息
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.weightx = 1.0;
        JLabel basicInfoLabel = new JLabel("星级管理");
        basicInfoLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        contentPanel.add(basicInfoLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        contentPanel.add(new JLabel("新建星级:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.starLevelField = new JTextField(15);
        contentPanel.add(this.starLevelField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("编辑现有星级:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.existingStarLevelComboBox = new JComboBox<String>();
        this.existingStarLevelComboBox.addActionListener(e -> loadExistingStarLevelData());
        contentPanel.add(this.existingStarLevelComboBox, gbc);

        // 属性配置区域
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.weightx = 1.0;
        JLabel attributeLabel = new JLabel("属性配置");
        attributeLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        contentPanel.add(attributeLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        contentPanel.add(new JLabel("攻击伤害:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.starAttackField = new JTextField("0", 12);
        contentPanel.add(this.starAttackField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("防御:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.starDefenseField = new JTextField("0", 12);
        contentPanel.add(this.starDefenseField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("吸血:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.starVampireField = new JTextField("0", 12);
        contentPanel.add(this.starVampireField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("跳跃:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.starJumpField = new JTextField("0", 12);
        contentPanel.add(this.starJumpField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("跌落减伤:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.starFallDamageField = new JTextField("0", 12);
        contentPanel.add(this.starFallDamageField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("反伤:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.starCounterAttackField = new JTextField("0", 12);
        contentPanel.add(this.starCounterAttackField, gbc);

        // 操作按钮
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        JButton saveBtn = new JButton("保存星级配置");
        saveBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        saveBtn.addActionListener(e -> saveStarConfiguration());
        contentPanel.add(saveBtn, gbc);

        gbc.gridx = 2;
        gbc.gridwidth = 2;
        JButton clearBtn = new JButton("清空表单");
        clearBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        clearBtn.addActionListener(e -> clearStarForm());
        contentPanel.add(clearBtn, gbc);

        return contentPanel;
    }

    /**
     * 创建星级配置右侧面板
     */
    private JPanel createStarRightPanel() {
        JPanel contentPanel = new JPanel(new java.awt.GridBagLayout());
        contentPanel.setBackground(java.awt.Color.WHITE);
        contentPanel.setBorder(BorderFactory.createTitledBorder("强化石成功几率配置"));
        contentPanel.setPreferredSize(new java.awt.Dimension(480, 500));

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 强化石几率配置标题
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.weightx = 1.0;
        JLabel probabilityLabel = new JLabel("强化石成功几率(%)");
        probabilityLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        contentPanel.add(probabilityLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        contentPanel.add(new JLabel("普通强化石:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.putongProbabilityField = new JTextField("100", 12);
        contentPanel.add(this.putongProbabilityField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("中等强化石:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.zhongdengProbabilityField = new JTextField("100", 12);
        contentPanel.add(this.zhongdengProbabilityField, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("高等强化石:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.gaodengProbabilityField = new JTextField("100", 12);
        contentPanel.add(this.gaodengProbabilityField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("完美强化石:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.wanmeiProbabilityField = new JTextField("100", 12);
        contentPanel.add(this.wanmeiProbabilityField, gbc);

        return contentPanel;
    }

    /**
     * 创建特效配置面板
     */
    private JPanel createEffectConfigPanel() {
        JPanel mainPanel = new JPanel(new java.awt.BorderLayout());
        mainPanel.setBackground(java.awt.Color.WHITE);

        // 创建左右分栏布局
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setDividerLocation(500);
        splitPane.setResizeWeight(0.5);
        splitPane.setBorder(null);

        // 左侧面板 - 星级特效管理
        JPanel leftPanel = createEffectLeftPanel();
        splitPane.setLeftComponent(leftPanel);

        // 右侧面板 - 特效创建和编辑
        JPanel rightPanel = createEffectRightPanel();
        splitPane.setRightComponent(rightPanel);

        mainPanel.add(splitPane, java.awt.BorderLayout.CENTER);

        // 初始化特效配置数据
        initializeEffectConfigData();

        return mainPanel;
    }

    /**
     * 创建特效配置左侧面板 - 星级特效管理
     */
    private JPanel createEffectLeftPanel() {
        JPanel contentPanel = new JPanel(new java.awt.GridBagLayout());
        contentPanel.setBackground(java.awt.Color.WHITE);
        contentPanel.setBorder(BorderFactory.createTitledBorder("星级特效管理"));
        contentPanel.setPreferredSize(new java.awt.Dimension(480, 600));

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 星级管理标题
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.weightx = 1.0;
        JLabel titleLabel = new JLabel("星级特效管理");
        titleLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        contentPanel.add(titleLabel, gbc);

        // 创建新星级
        row++;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        contentPanel.add(new JLabel("创建星级:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.newStarLevelField = new JTextField(10);
        this.newStarLevelField.setToolTipText("输入新的星级数字，如：1, 2, 3...");
        contentPanel.add(this.newStarLevelField, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        JButton createStarBtn = new JButton("创建");
        createStarBtn.addActionListener(e -> createNewStarLevel());
        contentPanel.add(createStarBtn, gbc);

        gbc.gridx = 3;
        gbc.weightx = 0.0;
        JButton deleteStarBtn = new JButton("删除");
        deleteStarBtn.addActionListener(e -> deleteSelectedStarLevel());
        contentPanel.add(deleteStarBtn, gbc);

        // 选择现有星级
        row++;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        contentPanel.add(new JLabel("选择星级:"), gbc);
        gbc.gridx = 1;
        gbc.gridwidth = 3;
        gbc.weightx = 1.0;
        this.effectStarLevelComboBox = new JComboBox<String>();
        this.effectStarLevelComboBox.addActionListener(e -> loadEffectsForSelectedLevel());
        contentPanel.add(this.effectStarLevelComboBox, gbc);

        // 当前特效列表
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.weightx = 1.0;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        gbc.weighty = 0;
        JLabel currentEffectsLabel = new JLabel("当前星级特效列表:");
        currentEffectsLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        contentPanel.add(currentEffectsLabel, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.fill = java.awt.GridBagConstraints.BOTH;
        gbc.weightx = 1.0;
        gbc.weighty = 0.4;
        this.currentEffectsModel = new DefaultListModel<String>();
        this.currentEffectsList = new JList<String>(this.currentEffectsModel);
        this.currentEffectsList.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        this.currentEffectsList.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 11));
        this.currentEffectsList.addListSelectionListener(e -> {
            if (!e.getValueIsAdjusting()) {
                loadSelectedEffectForEditing();
            }
        });
        JScrollPane currentEffectsScrollPane = new JScrollPane(this.currentEffectsList);
        currentEffectsScrollPane.setPreferredSize(new java.awt.Dimension(450, 150));
        contentPanel.add(currentEffectsScrollPane, gbc);

        // 特效操作按钮
        row++;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        gbc.weightx = 1.0;
        gbc.weighty = 0;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        JButton deleteEffectBtn = new JButton("删除选中特效");
        deleteEffectBtn.addActionListener(e -> removeSelectedEffect());
        contentPanel.add(deleteEffectBtn, gbc);

        gbc.gridx = 2;
        gbc.gridwidth = 2;
        JButton clearAllEffectsBtn = new JButton("清空所有特效");
        clearAllEffectsBtn.addActionListener(e -> clearAllEffectsForLevel());
        contentPanel.add(clearAllEffectsBtn, gbc);

        return contentPanel;
    }

    /**
     * 创建特效配置右侧面板 - 特效创建和编辑
     */
    private JPanel createEffectRightPanel() {
        JPanel contentPanel = new JPanel(new java.awt.GridBagLayout());
        contentPanel.setBackground(java.awt.Color.WHITE);
        contentPanel.setBorder(BorderFactory.createTitledBorder("特效创建和编辑"));
        contentPanel.setPreferredSize(new java.awt.Dimension(480, 600));

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 特效创建标题
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.weightx = 1.0;
        JLabel createLabel = new JLabel("特效创建和编辑");
        createLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        contentPanel.add(createLabel, gbc);

        // 特效类型选择
        row++;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        contentPanel.add(new JLabel("特效类型:"), gbc);
        gbc.gridx = 1;
        gbc.gridwidth = 3;
        gbc.weightx = 1.0;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        this.availableEffectTypeComboBox = new JComboBox<String>();
        this.availableEffectTypeComboBox.setModel(new DefaultComboBoxModel<String>(new String[] {
                "flame_basic - 基础火焰", "footstep - 脚步", "spiral_flame - 螺旋火焰",
                "rotating_halo - 旋转光环", "four_direction_spell - 四方向法术", "wings_56 - 5.6版本翅膀",
                "wings - 翅膀", "halo - 光环", "flame - 火焰", "nebula - 星云", "tornado - 龙卷风", "stars - 星星",
                "spiral - 螺旋", "sphere - 球体", "orbit - 轨道", "cube - 立方体", "helix - 螺旋桨",
                "batman - 蝙蝠侠", "popper - 爆炸螺旋", "pulse - 脉冲", "whirl - 旋涡", "whirlwind - 旋风",
                "invocation - 召唤法阵", "lightning - 雷电", "rainbow - 彩虹", "spacerift - 时空裂缝",
                "frost - 冰霜", "shadow - 暗影"
        }));
        contentPanel.add(this.availableEffectTypeComboBox, gbc);

        // 特效启用状态
        row++;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.fill = java.awt.GridBagConstraints.NONE;
        this.effectEnabledCheckBox = new JCheckBox("启用特效");
        this.effectEnabledCheckBox.setSelected(true);
        contentPanel.add(this.effectEnabledCheckBox, gbc);

        gbc.gridx = 1;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("叠加特效:"), gbc);
        gbc.gridx = 2;
        gbc.weightx = 0.0;
        JCheckBox stackingCheckBox = new JCheckBox("允许叠加");
        stackingCheckBox.setSelected(true);
        contentPanel.add(stackingCheckBox, gbc);

        // 颜色配置
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.weightx = 1.0;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        JLabel colorLabel = new JLabel("颜色配置 (支持自定义RGB值)");
        colorLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 12));
        contentPanel.add(colorLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        contentPanel.add(new JLabel("颜色1:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.effectColor1ComboBox = new JComboBox<String>();
        this.effectColor1ComboBox.setModel(new DefaultComboBoxModel<String>(new String[] {
                "红 (255,0,0)", "橙 (255,165,0)", "黄 (255,255,0)", "绿 (0,255,0)", "蓝 (0,0,255)",
                "紫 (128,0,128)", "粉 (255,192,203)", "黑 (0,0,0)", "白 (255,255,255)", "灰 (128,128,128)",
                "银 (192,192,192)", "金 (255,215,0)", "青 (0,255,255)", "自定义..."
        }));
        contentPanel.add(this.effectColor1ComboBox, gbc);

        gbc.gridx = 2;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("颜色2:"), gbc);
        gbc.gridx = 3;
        gbc.weightx = 1.0;
        this.effectColor2ComboBox = new JComboBox<String>();
        this.effectColor2ComboBox.setModel(new DefaultComboBoxModel<String>(new String[] {
                "红 (255,0,0)", "橙 (255,165,0)", "黄 (255,255,0)", "绿 (0,255,0)", "蓝 (0,0,255)",
                "紫 (128,0,128)", "粉 (255,192,203)", "黑 (0,0,0)", "白 (255,255,255)", "灰 (128,128,128)",
                "银 (192,192,192)", "金 (255,215,0)", "青 (0,255,255)", "自定义..."
        }));
        this.effectColor2ComboBox.setSelectedIndex(1); // 默认橙色
        contentPanel.add(this.effectColor2ComboBox, gbc);

        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("颜色3:"), gbc);
        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.effectColor3ComboBox = new JComboBox<String>();
        this.effectColor3ComboBox.setModel(new DefaultComboBoxModel<String>(new String[] {
                "红 (255,0,0)", "橙 (255,165,0)", "黄 (255,255,0)", "绿 (0,255,0)", "蓝 (0,0,255)",
                "紫 (128,0,128)", "粉 (255,192,203)", "黑 (0,0,0)", "白 (255,255,255)", "灰 (128,128,128)",
                "银 (192,192,192)", "金 (255,215,0)", "青 (0,255,255)", "自定义..."
        }));
        this.effectColor3ComboBox.setSelectedIndex(4); // 默认蓝色
        contentPanel.add(this.effectColor3ComboBox, gbc);

        // 操作按钮
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        JButton addEffectBtn = new JButton("添加特效到当前星级");
        addEffectBtn.addActionListener(e -> addEffectToCurrentLevel());
        contentPanel.add(addEffectBtn, gbc);

        gbc.gridx = 2;
        gbc.gridwidth = 2;
        JButton saveAllBtn = new JButton("保存所有特效配置");
        saveAllBtn.addActionListener(e -> saveAllEffectConfigurations());
        contentPanel.add(saveAllBtn, gbc);

        // 状态标签
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 4;
        gbc.anchor = java.awt.GridBagConstraints.CENTER;
        gbc.fill = java.awt.GridBagConstraints.NONE;
        this.statusLabel = new JLabel("就绪");
        this.statusLabel.setForeground(new java.awt.Color(0, 128, 0));
        contentPanel.add(this.statusLabel, gbc);

        return contentPanel;
    }

    /**
     * 创建符咒管理面板
     */
    private JPanel createCharmManagementPanel() {
        JPanel mainPanel = new JPanel(new java.awt.BorderLayout());
        mainPanel.setBackground(java.awt.Color.WHITE);

        // 创建可调节大小的分割面板 - 与玩家管理保持一致
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setDividerLocation(400); // 与玩家管理一致的初始分割位置
        splitPane.setResizeWeight(0.4); // 与玩家管理一致的左侧权重40%
        splitPane.setBorder(null);
        splitPane.setBackground(java.awt.Color.WHITE);

        // 左侧面板 - 在线玩家选择
        JPanel leftPanel = createCharmLeftPanel();
        splitPane.setLeftComponent(leftPanel);

        // 右侧面板 - 符咒配置和操作
        JPanel rightPanel = createCharmRightPanel();
        splitPane.setRightComponent(rightPanel);

        mainPanel.add(splitPane, java.awt.BorderLayout.CENTER);

        // 初始化符咒数据
        initializeCharmData();

        return mainPanel;
    }

    /**
     * 创建符咒管理左侧面板 - 在线玩家列表
     */
    private JPanel createCharmLeftPanel() {
        JPanel leftPanel = new JPanel(new java.awt.BorderLayout());
        leftPanel.setBackground(java.awt.Color.WHITE);
        leftPanel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createEtchedBorder(), "在线玩家列表",
                javax.swing.border.TitledBorder.DEFAULT_JUSTIFICATION,
                javax.swing.border.TitledBorder.DEFAULT_POSITION,
                new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14)));

        // 创建符咒专用的玩家网格面板
        this.charmPlayersGridPanel = new PlayerGridPanel();
        JScrollPane scrollPane = new JScrollPane(this.charmPlayersGridPanel);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_NEVER);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setPreferredSize(new java.awt.Dimension(350, 450));
        leftPanel.add(scrollPane, java.awt.BorderLayout.CENTER);

        // 底部操作按钮
        JPanel buttonPanel = new JPanel(new java.awt.FlowLayout(java.awt.FlowLayout.CENTER, 10, 5));
        buttonPanel.setBackground(java.awt.Color.WHITE);

        JButton refreshBtn = new JButton("刷新在线玩家");
        refreshBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        refreshBtn.setPreferredSize(new java.awt.Dimension(120, 30));
        refreshBtn.addActionListener(e -> refreshCharmOnlinePlayers());
        buttonPanel.add(refreshBtn);

        JButton clearBtn = new JButton("清除选择");
        clearBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        clearBtn.setPreferredSize(new java.awt.Dimension(100, 30));
        clearBtn.addActionListener(e -> {
            if (charmPlayersGridPanel != null) {
                charmPlayersGridPanel.clearSelection();
            }
            if (charmTargetPlayerField != null) {
                charmTargetPlayerField.setText("");
            }
        });
        buttonPanel.add(clearBtn);

        leftPanel.add(buttonPanel, java.awt.BorderLayout.SOUTH);

        return leftPanel;
    }

    /**
     * 创建符咒管理右侧面板 - 符咒配置和操作
     */
    private JPanel createCharmRightPanel() {
        JPanel contentPanel = new JPanel(new java.awt.GridBagLayout());
        contentPanel.setBackground(java.awt.Color.WHITE);
        contentPanel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createEtchedBorder(), "符咒配置",
                javax.swing.border.TitledBorder.DEFAULT_JUSTIFICATION,
                javax.swing.border.TitledBorder.DEFAULT_POSITION,
                new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14)));

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(8, 8, 8, 8);
        gbc.anchor = java.awt.GridBagConstraints.WEST;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        int row = 0;

        // 离线玩家输入
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        JLabel offlineLabel = new JLabel("离线玩家名称:");
        offlineLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        contentPanel.add(offlineLabel, gbc);

        gbc.gridx = 1;
        gbc.gridwidth = 1;
        gbc.weightx = 1.0;
        this.charmTargetPlayerField = new JTextField();
        this.charmTargetPlayerField.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
        this.charmTargetPlayerField.setPreferredSize(new java.awt.Dimension(200, 30));
        this.charmTargetPlayerField.setToolTipText("输入离线玩家名称，或从左侧选择在线玩家");
        contentPanel.add(this.charmTargetPlayerField, gbc);

        // 符咒类型配置
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        JLabel typeLabel = new JLabel("符咒类型配置");
        typeLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        contentPanel.add(typeLabel, gbc);

        row++;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.gridx = 0;
        gbc.gridy = row;
        contentPanel.add(new JLabel("符咒类型:"), gbc);

        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.charmTypeComboBox = new JComboBox<String>();
        this.charmTypeComboBox.addItem("淬炼直升棒");
        this.charmTypeComboBox.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        this.charmTypeComboBox.setToolTipText("目前只支持淬炼直升棒，会自动检测配置的总星级数");
        contentPanel.add(this.charmTypeComboBox, gbc);

        // 符咒等级配置
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("目标星级:"), gbc);

        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.charmLevelComboBox = new JComboBox<String>();
        this.charmLevelComboBox.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        this.charmLevelComboBox.setToolTipText("选择符咒升级到的目标星级");
        contentPanel.add(this.charmLevelComboBox, gbc);

        // 数量配置
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.weightx = 0.0;
        contentPanel.add(new JLabel("数量:"), gbc);

        gbc.gridx = 1;
        gbc.weightx = 1.0;
        this.charmQuantityField = new JTextField("1", 10);
        this.charmQuantityField.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        this.charmQuantityField.setToolTipText("输入要给予的符咒数量");
        contentPanel.add(this.charmQuantityField, gbc);

        // 操作按钮
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        JButton giveCharmBtn = new JButton("给予符咒");
        giveCharmBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        giveCharmBtn.addActionListener(e -> giveCharmToPlayer());
        contentPanel.add(giveCharmBtn, gbc);

        // 说明文本
        row++;
        gbc.gridx = 0;
        gbc.gridy = row;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        gbc.weighty = 1.0;
        gbc.fill = java.awt.GridBagConstraints.BOTH;

        javax.swing.JTextArea infoArea = new javax.swing.JTextArea();
        infoArea.setText("符咒说明:\n\n" +
                "• 淬炼直升棒: 可以直接将装备升级到指定星级\n" +
                "• 自动检测: 系统会自动检测配置文件中的最大星级\n" +
                "• 实时更新: 当星级配置更新时，符咒等级选项会自动刷新\n" +
                "• 支持离线: 可以给离线玩家发送符咒");
        infoArea.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 11));
        infoArea.setEditable(false);
        infoArea.setBackground(new java.awt.Color(245, 245, 245));
        infoArea.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        infoArea.setLineWrap(true);
        infoArea.setWrapStyleWord(true);

        JScrollPane infoScrollPane = new JScrollPane(infoArea);
        infoScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        infoScrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
        contentPanel.add(infoScrollPane, gbc);

        return contentPanel;
    }

    /**
     * 创建游戏内GUI管理面板
     */
    private JPanel createInGameGUIPanel() {
        JPanel mainPanel = new JPanel(new java.awt.BorderLayout());
        mainPanel.setBackground(java.awt.Color.WHITE);

        // 创建左右分栏布局
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setDividerLocation(400);
        splitPane.setResizeWeight(0.4);
        splitPane.setBorder(null);

        // 左侧面板 - 在线玩家选择
        JPanel leftPanel = createInGameGUILeftPanel();
        splitPane.setLeftComponent(leftPanel);

        // 右侧面板 - GUI功能管理
        JPanel rightPanel = createInGameGUIRightPanel();
        splitPane.setRightComponent(rightPanel);

        mainPanel.add(splitPane, java.awt.BorderLayout.CENTER);

        return mainPanel;
    }

    /**
     * 创建游戏内GUI左侧面板 - 在线玩家列表（与玩家管理一模一样）
     */
    private JPanel createInGameGUILeftPanel() {
        JPanel leftPanel = new JPanel(new java.awt.BorderLayout());
        leftPanel.setBackground(java.awt.Color.WHITE);
        leftPanel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createEtchedBorder(), "在线玩家列表",
                javax.swing.border.TitledBorder.DEFAULT_JUSTIFICATION,
                javax.swing.border.TitledBorder.DEFAULT_POSITION,
                new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14)));

        // 创建多列玩家网格面板（与玩家管理完全一样）
        guiPlayersGridPanel = new PlayerGridPanel();
        JScrollPane scrollPane = new JScrollPane(guiPlayersGridPanel);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_NEVER);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setPreferredSize(new java.awt.Dimension(350, 450));
        leftPanel.add(scrollPane, java.awt.BorderLayout.CENTER);

        // 底部操作按钮（与玩家管理完全一样）
        JPanel buttonPanel = new JPanel(new java.awt.FlowLayout(java.awt.FlowLayout.CENTER, 10, 5));
        buttonPanel.setBackground(java.awt.Color.WHITE);

        JButton refreshBtn = new JButton("刷新在线玩家");
        refreshBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        refreshBtn.setPreferredSize(new java.awt.Dimension(120, 30));
        refreshBtn.addActionListener(e -> refreshGUIPlayersList());
        buttonPanel.add(refreshBtn);

        JButton clearBtn = new JButton("清除选择");
        clearBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        clearBtn.setPreferredSize(new java.awt.Dimension(100, 30));
        clearBtn.addActionListener(e -> {
            if (guiPlayersGridPanel != null) {
                guiPlayersGridPanel.clearSelection();
            }
            if (guiPlayerNameField != null) {
                guiPlayerNameField.setText("");
            }
        });
        buttonPanel.add(clearBtn);

        leftPanel.add(buttonPanel, java.awt.BorderLayout.SOUTH);

        // 初始刷新玩家列表
        refreshGUIPlayersList();

        // 启动定时器，每30秒自动刷新一次玩家列表
        javax.swing.Timer guiRefreshTimer = new javax.swing.Timer(30000, e -> refreshGUIPlayersList());
        guiRefreshTimer.start();

        return leftPanel;
    }

    /**
     * 创建游戏内GUI右侧面板
     */
    private JPanel createInGameGUIRightPanel() {
        JPanel rightPanel = new JPanel();
        rightPanel.setLayout(new java.awt.GridBagLayout());
        rightPanel.setBackground(java.awt.Color.WHITE);
        rightPanel.setBorder(BorderFactory.createTitledBorder(
                BorderFactory.createLineBorder(new java.awt.Color(200, 200, 200), 1),
                "游戏内GUI功能管理",
                javax.swing.border.TitledBorder.DEFAULT_JUSTIFICATION,
                javax.swing.border.TitledBorder.DEFAULT_POSITION,
                new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14),
                new java.awt.Color(60, 60, 60)));

        java.awt.GridBagConstraints gbc = new java.awt.GridBagConstraints();
        gbc.insets = new java.awt.Insets(10, 10, 10, 10);
        gbc.anchor = java.awt.GridBagConstraints.WEST;

        // 手动输入玩家名称区域
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.gridwidth = 1;
        gbc.weightx = 0.0;
        gbc.fill = java.awt.GridBagConstraints.NONE;
        JLabel offlineLabel = new JLabel("离线玩家名称:");
        offlineLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.BOLD, 14));
        rightPanel.add(offlineLabel, gbc);

        gbc.gridx = 1;
        gbc.gridwidth = 1;
        gbc.weightx = 1.0;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        guiPlayerNameField = new JTextField();
        guiPlayerNameField.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
        guiPlayerNameField.setPreferredSize(new java.awt.Dimension(300, 30));
        guiPlayerNameField.setToolTipText("如果玩家列表为空，可以手动输入玩家名称");
        guiPlayerNameField.addKeyListener(new java.awt.event.KeyAdapter() {
            @Override
            public void keyTyped(java.awt.event.KeyEvent e) {
                if (!guiPlayerNameField.getText().trim().isEmpty()) {
                    if (guiPlayersGridPanel != null) {
                        guiPlayersGridPanel.clearSelection();
                    }
                }
            }
        });
        rightPanel.add(guiPlayerNameField, gbc);

        // GUI功能说明
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.gridwidth = 2;
        gbc.weightx = 1.0;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;
        JLabel infoLabel = new JLabel("<html><div style='width:400px;'>" +
                "<h3 style='color:#2E8B57;'>游戏内GUI功能说明</h3>" +
                "<p><b>装备管理界面：</b>玩家可以查看当前装备信息、套装状态等</p>" +
                "<p><b>套装预览界面：</b>玩家可以预览所有可用套装的属性和效果</p>" +
                "<p><b>特效设置界面：</b>玩家可以开关特效、查看当前特效状态</p>" +
                "<p><b>淬炼信息界面：</b>玩家可以查看装备和武器的淬炼等级</p>" +
                "</div></html>");
        infoLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        rightPanel.add(infoLabel, gbc);

        // GUI功能按钮区域
        gbc.gridwidth = 1;
        gbc.fill = java.awt.GridBagConstraints.HORIZONTAL;

        // 为选中玩家打开装备管理界面
        JButton openEquipmentGUIBtn = new JButton("打开装备管理界面");
        openEquipmentGUIBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        openEquipmentGUIBtn.setBackground(new java.awt.Color(46, 139, 87));
        openEquipmentGUIBtn.setForeground(java.awt.Color.WHITE);
        openEquipmentGUIBtn.setFocusPainted(false);
        openEquipmentGUIBtn.addActionListener(e -> openGUIForSelectedPlayer("equipment"));
        gbc.gridx = 0;
        gbc.gridy = 2;
        rightPanel.add(openEquipmentGUIBtn, gbc);

        // 为选中玩家打开套装预览界面
        JButton openSuitPreviewGUIBtn = new JButton("打开套装预览界面");
        openSuitPreviewGUIBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        openSuitPreviewGUIBtn.setBackground(new java.awt.Color(30, 144, 255));
        openSuitPreviewGUIBtn.setForeground(java.awt.Color.WHITE);
        openSuitPreviewGUIBtn.setFocusPainted(false);
        openSuitPreviewGUIBtn.addActionListener(e -> openGUIForSelectedPlayer("suit"));
        gbc.gridx = 1;
        gbc.gridy = 2;
        rightPanel.add(openSuitPreviewGUIBtn, gbc);

        // 为选中玩家打开特效设置界面
        JButton openEffectGUIBtn = new JButton("打开特效设置界面");
        openEffectGUIBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        openEffectGUIBtn.setBackground(new java.awt.Color(255, 140, 0));
        openEffectGUIBtn.setForeground(java.awt.Color.WHITE);
        openEffectGUIBtn.setFocusPainted(false);
        openEffectGUIBtn.addActionListener(e -> openGUIForSelectedPlayer("effect"));
        gbc.gridx = 0;
        gbc.gridy = 3;
        rightPanel.add(openEffectGUIBtn, gbc);

        // 为选中玩家打开淬炼信息界面
        JButton openEnhanceGUIBtn = new JButton("打开淬炼信息界面");
        openEnhanceGUIBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        openEnhanceGUIBtn.setBackground(new java.awt.Color(220, 20, 60));
        openEnhanceGUIBtn.setForeground(java.awt.Color.WHITE);
        openEnhanceGUIBtn.setFocusPainted(false);
        openEnhanceGUIBtn.addActionListener(e -> openGUIForSelectedPlayer("enhance"));
        gbc.gridx = 1;
        gbc.gridy = 3;
        rightPanel.add(openEnhanceGUIBtn, gbc);

        // 添加命令说明
        JLabel commandLabel = new JLabel("<html><div style='width:400px;'>" +
                "<h3 style='color:#B22222;'>相关命令</h3>" +
                "<p><b>/cuilian gui equipment</b> - 打开装备管理界面</p>" +
                "<p><b>/cuilian gui suit</b> - 打开套装预览界面</p>" +
                "<p><b>/cuilian gui effect</b> - 打开特效设置界面</p>" +
                "<p><b>/cuilian gui enhance</b> - 打开淬炼信息界面</p>" +
                "</div></html>");
        commandLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 11));
        gbc.gridx = 0;
        gbc.gridy = 4;
        gbc.gridwidth = 2;
        rightPanel.add(commandLabel, gbc);

        return rightPanel;
    }

    /**
     * 刷新游戏内GUI管理的玩家列表
     */
    private void refreshGUIPlayersList() {
        if (guiPlayersGridPanel == null) {
            return;
        }

        java.util.List<String> players = new java.util.ArrayList<>();
        try {
            for (org.bukkit.entity.Player player : org.bukkit.Bukkit.getOnlinePlayers()) {
                players.add(player.getName());
            }
        } catch (Exception e) {
            // 如果Bukkit不可用，添加测试数据
            for (int i = 1; i <= 10; i++) {
                players.add("测试玩家" + i);
            }
        }

        guiPlayersGridPanel.updatePlayers(players);
    }

    /**
     * 为选中的玩家打开指定的GUI界面
     */
    private void openGUIForSelectedPlayer(String guiType) {
        // 获取选中的玩家
        String tempSelectedPlayer = null;

        // 优先使用网格面板中选中的玩家
        if (guiPlayersGridPanel != null) {
            tempSelectedPlayer = guiPlayersGridPanel.getSelectedPlayer();
        }

        // 如果没有选中玩家，使用手动输入的玩家名称
        if (tempSelectedPlayer == null || tempSelectedPlayer.isEmpty()) {
            if (guiPlayerNameField != null) {
                tempSelectedPlayer = guiPlayerNameField.getText().trim();
            }
        }

        if (tempSelectedPlayer == null || tempSelectedPlayer.isEmpty()) {
            JOptionPane.showMessageDialog(null, "请先选择一个玩家或在输入框中输入玩家名称！");
            return;
        }

        // 创建final变量供lambda使用
        final String selectedPlayer = tempSelectedPlayer;

        try {
            org.bukkit.entity.Player player = org.bukkit.Bukkit.getPlayer(selectedPlayer);
            if (player != null && player.isOnline()) {
                // 先关闭玩家当前打开的界面，避免冲突
                player.closeInventory();

                // 延迟一小段时间再打开新界面，确保旧界面完全关闭
                javax.swing.Timer delayTimer = new javax.swing.Timer(100, e -> {
                    try {
                        switch (guiType) {
                            case "equipment":
                                cn.winde.cuilian.gui.InGameGUI.openEquipmentGUI(player);
                                javax.swing.SwingUtilities.invokeLater(() -> JOptionPane.showMessageDialog(null,
                                        "已为玩家 " + selectedPlayer + " 打开装备管理界面"));
                                break;
                            case "suit":
                                cn.winde.cuilian.gui.InGameGUI.openSuitPreviewGUI(player);
                                javax.swing.SwingUtilities.invokeLater(() -> JOptionPane.showMessageDialog(null,
                                        "已为玩家 " + selectedPlayer + " 打开套装预览界面"));
                                break;
                            case "effect":
                                cn.winde.cuilian.gui.InGameGUI.openEffectSettingsGUI(player);
                                javax.swing.SwingUtilities.invokeLater(() -> JOptionPane.showMessageDialog(null,
                                        "已为玩家 " + selectedPlayer + " 打开特效设置界面"));
                                break;
                            case "enhance":
                                cn.winde.cuilian.gui.InGameGUI.openEnhanceInfoGUI(player);
                                javax.swing.SwingUtilities.invokeLater(() -> JOptionPane.showMessageDialog(null,
                                        "已为玩家 " + selectedPlayer + " 打开淬炼信息界面"));
                                break;
                        }
                    } catch (Exception ex) {
                        javax.swing.SwingUtilities.invokeLater(
                                () -> JOptionPane.showMessageDialog(null, "打开GUI界面时出现错误: " + ex.getMessage()));
                    }
                });
                delayTimer.setRepeats(false);
                delayTimer.start();

            } else {
                JOptionPane.showMessageDialog(null, "玩家 " + selectedPlayer + " 不在线或不存在！");
            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "操作失败: " + e.getMessage());
        }
    }

    /**
     * 重新加载所有配置文件
     */
    private void reloadAllConfigs() throws Exception {
        try {
            Cuilian instance = Cuilian.getInstance();
            if (instance != null) {
                // 重新加载主配置文件
                instance.reloadConfig();

                // 重新加载套装配置
                if (Cuilian.Suit != null) {
                    Cuilian.Suit = org.bukkit.configuration.file.YamlConfiguration.loadConfiguration(
                            new java.io.File(instance.getDataFolder(), "Suit.yml"));
                }

                // 重新加载武器配置
                if (Cuilian.Weapon != null) {
                    Cuilian.Weapon = org.bukkit.configuration.file.YamlConfiguration.loadConfiguration(
                            new java.io.File(instance.getDataFolder(), "Weapon.yml"));
                }

                // 重新加载配置到内存
                instance.loadyml();

                // 更新UI显示
                javax.swing.SwingUtilities.invokeLater(() -> {
                    // 刷新套装列表
                    loadAvailableSuits();
                    loadDeleteSuitList();
                    // 更新特效开关状态
                    updateEffectCheckboxState();
                });

                // 通知所有打开特效设置界面的玩家更新界面
                if (org.bukkit.Bukkit.getServer() != null) {
                    org.bukkit.Bukkit.getScheduler().runTaskLater(
                            cn.winde.cuilian.Cuilian.getInstance(), () -> {
                                cn.winde.cuilian.gui.InGameGUIListener.notifyConfigReload();
                            }, 2L);
                }

                System.out.println("所有配置文件已重新加载");
            } else {
                throw new Exception("插件实例不可用");
            }

        } catch (Exception e) {
            System.err.println("重新加载配置文件时出现错误: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 更新特效开关状态
     */
    private void updateEffectCheckboxState() {
        if (checkBox != null && Cuilian.config != null) {
            boolean effectsEnabled = Cuilian.config.getBoolean("effects", true);
            checkBox.setSelected(effectsEnabled);
        }

        // 同时更新TPS自动特效开关状态
        if (tpsAutoEffectCheckBox != null && Cuilian.config != null) {
            boolean tpsAutoEnabled = Cuilian.config.getBoolean("tps_auto_effect.enabled", false);
            tpsAutoEffectCheckBox.setSelected(tpsAutoEnabled);
        }

        // 更新TPS阈值显示
        if (tpsThresholdField != null && Cuilian.config != null) {
            double threshold = Cuilian.config.getDouble("tps_auto_effect.threshold", 15.0);
            tpsThresholdField.setText(String.valueOf(threshold));
        }

        // 更新TPS恢复延迟显示
        if (tpsDelayField != null && Cuilian.config != null) {
            int delay = Cuilian.config.getInt("tps_auto_effect.recovery_delay", 30);
            tpsDelayField.setText(String.valueOf(delay));
        }

        // 更新预览冷却时间显示
        if (previewCooldownField != null && Cuilian.config != null) {
            int cooldown = Cuilian.config.getInt("suit_preview.cooldown", 60);
            previewCooldownField.setText(String.valueOf(cooldown));
        }

        // 更新套装特效消息强制禁用开关状态
        if (forceDisableMessagesCheckBox != null && Cuilian.config != null) {
            boolean forceDisabled = Cuilian.config.getBoolean("suit_effect_messages.force_disabled", false);
            forceDisableMessagesCheckBox.setSelected(forceDisabled);
        }
    }

    /**
     * 保存TPS阈值设置
     */
    private void saveTpsThreshold() {
        try {
            String thresholdText = tpsThresholdField.getText().trim();
            double threshold = Double.parseDouble(thresholdText);

            if (threshold < 1.0 || threshold > 20.0) {
                JOptionPane.showMessageDialog(null, "TPS阈值必须在1.0到20.0之间！");
                tpsThresholdField.setText(String.valueOf(Cuilian.config.getDouble("tps_auto_effect.threshold", 15.0)));
                return;
            }

            Cuilian instance = Cuilian.getInstance();
            if (instance != null) {
                instance.getConfig().set("tps_auto_effect.threshold", threshold);
                instance.saveConfig();
                Cuilian.config.set("tps_auto_effect.threshold", threshold);

                System.out.println("TPS阈值已设置为: " + threshold);
            } else {
                JOptionPane.showMessageDialog(null, "插件实例不可用，无法保存配置");
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "请输入有效的数字！");
            tpsThresholdField.setText(String.valueOf(Cuilian.config.getDouble("tps_auto_effect.threshold", 15.0)));
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "保存TPS阈值失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 保存TPS恢复延迟设置
     */
    private void saveTpsDelay() {
        try {
            String delayText = tpsDelayField.getText().trim();
            int delay = Integer.parseInt(delayText);

            if (delay < 5 || delay > 300) {
                JOptionPane.showMessageDialog(null, "恢复延迟必须在5到300秒之间！");
                tpsDelayField.setText(String.valueOf(Cuilian.config.getInt("tps_auto_effect.recovery_delay", 30)));
                return;
            }

            Cuilian instance = Cuilian.getInstance();
            if (instance != null) {
                instance.getConfig().set("tps_auto_effect.recovery_delay", delay);
                instance.saveConfig();
                Cuilian.config.set("tps_auto_effect.recovery_delay", delay);

                System.out.println("TPS恢复延迟已设置为: " + delay + "秒");
            } else {
                JOptionPane.showMessageDialog(null, "插件实例不可用，无法保存配置");
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "请输入有效的数字！");
            tpsDelayField.setText(String.valueOf(Cuilian.config.getInt("tps_auto_effect.recovery_delay", 30)));
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "保存TPS恢复延迟失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 保存TPS恢复延迟设置
     */
    private void saveTpsRecoveryDelay() {
        try {
            String delayText = tpsRecoveryDelayField.getText().trim();
            int delay = Integer.parseInt(delayText);

            if (delay < 5 || delay > 300) {
                JOptionPane.showMessageDialog(null, "恢复延迟必须在5到300秒之间！");
                tpsRecoveryDelayField
                        .setText(String.valueOf(Cuilian.config.getInt("tps_auto_effect.recovery_delay", 10)));
                return;
            }

            Cuilian instance = Cuilian.getInstance();
            if (instance != null) {
                instance.getConfig().set("tps_auto_effect.recovery_delay", delay);
                instance.saveConfig();
                Cuilian.config.set("tps_auto_effect.recovery_delay", delay);

                System.out.println("TPS恢复延迟已设置为: " + delay + "秒");
            } else {
                JOptionPane.showMessageDialog(null, "插件实例不可用，无法保存配置");
            }
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "请输入有效的数字！");
            tpsRecoveryDelayField.setText(String.valueOf(Cuilian.config.getInt("tps_auto_effect.recovery_delay", 10)));
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "保存TPS恢复延迟失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 保存套装预览时间设置
     */
    private void savePreviewDuration() {
        try {
            String durationText = previewDurationField.getText().trim();
            int duration = Integer.parseInt(durationText);

            if (duration < 5 || duration > 300) {
                JOptionPane.showMessageDialog(null, "预览时间必须在5到300秒之间！");
                previewDurationField.setText(String.valueOf(Cuilian.config.getInt("suit_preview.duration", 30)));
                return;
            }

            Cuilian instance = Cuilian.getInstance();
            if (instance != null) {
                instance.getConfig().set("suit_preview.duration", duration);
                instance.saveConfig();
                Cuilian.config.set("suit_preview.duration", duration);

                System.out.println("套装预览时间已设置为: " + duration + "秒");
            } else {
                JOptionPane.showMessageDialog(null, "插件实例不可用，无法保存配置");
            }

        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "请输入有效的数字！");
            previewDurationField.setText(String.valueOf(Cuilian.config.getInt("suit_preview.duration", 30)));
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "保存预览时间失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 保存套装预览冷却时间设置
     */
    private void savePreviewCooldown() {
        try {
            String cooldownText = previewCooldownField.getText().trim();
            int cooldown = Integer.parseInt(cooldownText);

            if (cooldown < 0 || cooldown > 3600) {
                JOptionPane.showMessageDialog(null, "预览冷却时间必须在0到3600秒之间！");
                previewCooldownField.setText(String.valueOf(Cuilian.config.getInt("suit_preview.cooldown", 60)));
                return;
            }

            Cuilian instance = Cuilian.getInstance();
            if (instance != null) {
                instance.getConfig().set("suit_preview.cooldown", cooldown);
                instance.saveConfig();
                Cuilian.config.set("suit_preview.cooldown", cooldown);

                System.out.println("套装预览冷却时间已设置为: " + cooldown + "秒");
            } else {
                JOptionPane.showMessageDialog(null, "插件实例不可用，无法保存配置");
            }

        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "请输入有效的数字！");
            previewCooldownField.setText(String.valueOf(Cuilian.config.getInt("suit_preview.cooldown", 60)));
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "保存预览冷却时间失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 发送特效开关公告
     */
    private void sendEffectToggleAnnouncement(boolean enabled) {
        try {
            if (enabled) {
                // 特效启用公告
                org.bukkit.Bukkit.broadcastMessage("§6§l===========================================");
                org.bukkit.Bukkit.broadcastMessage("§a§l[特效] 服务器特效系统已启用！");
                org.bukkit.Bukkit.broadcastMessage("§e§l[公告] 所有套装特效现在可以正常显示");
                org.bukkit.Bukkit.broadcastMessage("§c§l[注意] 需要重新穿戴装备才能激活特效");
                org.bukkit.Bukkit.broadcastMessage("§7§l[提示] 脱下装备再重新穿上即可激活特效");
                org.bukkit.Bukkit.broadcastMessage("§6§l===========================================");

                // 给所有在线玩家发送标题提示
                for (org.bukkit.entity.Player player : org.bukkit.Bukkit.getOnlinePlayers()) {
                    player.sendTitle("§a§l特效已启用", "§e重新穿戴装备激活特效");
                }
            } else {
                // 特效禁用公告
                org.bukkit.Bukkit.broadcastMessage("§6§l===========================================");
                org.bukkit.Bukkit.broadcastMessage("§c§l[特效] 服务器特效系统已禁用！");
                org.bukkit.Bukkit.broadcastMessage("§e§l[公告] 所有套装特效已停止显示");
                org.bukkit.Bukkit.broadcastMessage("§7§l[系统] 管理员已暂时关闭特效功能");
                org.bukkit.Bukkit.broadcastMessage("§6§l===========================================");

                // 给所有在线玩家发送标题提示
                for (org.bukkit.entity.Player player : org.bukkit.Bukkit.getOnlinePlayers()) {
                    player.sendTitle("§c§l特效已禁用", "§7服务器已关闭特效显示");
                }
            }

            System.out.println("已发送特效" + (enabled ? "启用" : "禁用") + "公告给所有在线玩家");
        } catch (Exception e) {
            System.err.println("发送特效公告时出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建底部面板
     */
    private JPanel createBottomPanel() {
        JPanel bottomPanel = new JPanel(new java.awt.FlowLayout(java.awt.FlowLayout.LEFT));
        bottomPanel.setBackground(java.awt.Color.WHITE);
        bottomPanel.setBorder(BorderFactory.createEmptyBorder(5, 10, 5, 10));

        // 特效开关
        this.checkBox = new JCheckBox("启用特效");
        this.checkBox.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        // 从配置文件读取当前状态
        this.checkBox.setSelected(Cuilian.config.getBoolean("effects", true));
        this.checkBox.addActionListener(e -> {
            boolean enabled = checkBox.isSelected();
            try {
                // 直接更新配置文件
                Cuilian instance = Cuilian.getInstance();
                if (instance != null) {
                    instance.getConfig().set("effects", enabled);
                    instance.saveConfig();

                    // 更新内存中的配置
                    Cuilian.config.set("effects", enabled);

                    // 显示状态消息
                    String message = enabled ? "全服特效已启用！" : "全服特效已禁用！";
                    JOptionPane.showMessageDialog(null, message);

                    // 发送全服公告
                    sendEffectToggleAnnouncement(enabled);

                    // 如果禁用特效，清空所有玩家的特效
                    if (!enabled) {
                        Cuilian.lizi.clear();
                        System.out.println("已清空所有玩家特效");
                    }

                    System.out.println("特效全局开关已" + (enabled ? "启用" : "禁用"));
                } else {
                    JOptionPane.showMessageDialog(null, "插件实例不可用，无法保存配置");
                }
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(null, "保存配置失败: " + ex.getMessage());
                ex.printStackTrace();
            }
        });
        bottomPanel.add(this.checkBox);

        // TPS自动特效开关
        tpsAutoEffectCheckBox = new JCheckBox("TPS自动特效开关");
        tpsAutoEffectCheckBox.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        tpsAutoEffectCheckBox.setSelected(Cuilian.config.getBoolean("tps_auto_effect.enabled", false));
        tpsAutoEffectCheckBox.setToolTipText("当服务器TPS过低时自动禁用特效");
        tpsAutoEffectCheckBox.addActionListener(e -> {
            boolean enabled = tpsAutoEffectCheckBox.isSelected();
            try {
                Cuilian instance = Cuilian.getInstance();
                if (instance != null) {
                    instance.getConfig().set("tps_auto_effect.enabled", enabled);
                    instance.saveConfig();
                    Cuilian.config.set("tps_auto_effect.enabled", enabled);

                    // 启动或停止TPS监控器
                    if (enabled) {
                        cn.winde.cuilian.tps.TPSMonitor.startMonitoring();
                    } else {
                        cn.winde.cuilian.tps.TPSMonitor.stopMonitoring();
                    }

                    String message = enabled ? "TPS自动特效开关已启用！" : "TPS自动特效开关已禁用！";
                    JOptionPane.showMessageDialog(null, message);

                    System.out.println("TPS自动特效开关已" + (enabled ? "启用" : "禁用"));
                } else {
                    JOptionPane.showMessageDialog(null, "插件实例不可用，无法保存配置");
                }
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(null, "保存配置失败: " + ex.getMessage());
                ex.printStackTrace();
            }
        });
        bottomPanel.add(tpsAutoEffectCheckBox);

        // TPS阈值设置
        JLabel tpsLabel = new JLabel("TPS阈值:");
        tpsLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        bottomPanel.add(tpsLabel);

        tpsThresholdField = new JTextField(5);
        tpsThresholdField.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        tpsThresholdField.setText(String.valueOf(Cuilian.config.getDouble("tps_auto_effect.threshold", 15.0)));
        tpsThresholdField.setToolTipText("当TPS低于此值时自动禁用特效");
        tpsThresholdField.addActionListener(e -> saveTpsThreshold());
        tpsThresholdField.addFocusListener(new java.awt.event.FocusAdapter() {
            @Override
            public void focusLost(java.awt.event.FocusEvent e) {
                saveTpsThreshold();
            }
        });
        bottomPanel.add(tpsThresholdField);

        // TPS恢复延迟设置
        JLabel recoveryDelayLabel = new JLabel("恢复延迟(秒):");
        recoveryDelayLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        bottomPanel.add(recoveryDelayLabel);

        tpsRecoveryDelayField = new JTextField(5);
        tpsRecoveryDelayField.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        tpsRecoveryDelayField.setText(String.valueOf(Cuilian.config.getInt("tps_auto_effect.recovery_delay", 10)));
        tpsRecoveryDelayField.setToolTipText("TPS恢复正常后等待多少秒再重新启用特效");
        tpsRecoveryDelayField.addActionListener(e -> saveTpsRecoveryDelay());
        tpsRecoveryDelayField.addFocusListener(new java.awt.event.FocusAdapter() {
            @Override
            public void focusLost(java.awt.event.FocusEvent e) {
                saveTpsRecoveryDelay();
            }
        });
        bottomPanel.add(tpsRecoveryDelayField);

        // 套装预览时间设置
        JLabel previewDurationLabel = new JLabel("预览时间(秒):");
        previewDurationLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        bottomPanel.add(previewDurationLabel);

        previewDurationField = new JTextField(5);
        previewDurationField.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        previewDurationField.setText(String.valueOf(Cuilian.config.getInt("suit_preview.duration", 30)));
        previewDurationField.setToolTipText("套装预览特效持续时间");
        previewDurationField.addActionListener(e -> savePreviewDuration());
        previewDurationField.addFocusListener(new java.awt.event.FocusAdapter() {
            @Override
            public void focusLost(java.awt.event.FocusEvent e) {
                savePreviewDuration();
            }
        });
        bottomPanel.add(previewDurationField);

        // 套装预览冷却时间设置
        JLabel previewCooldownLabel = new JLabel("预览冷却(秒):");
        previewCooldownLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        bottomPanel.add(previewCooldownLabel);

        previewCooldownField = new JTextField(5);
        previewCooldownField.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        previewCooldownField.setText(String.valueOf(Cuilian.config.getInt("suit_preview.cooldown", 60)));
        previewCooldownField.setToolTipText("套装预览功能冷却时间，设置为0表示无冷却");
        previewCooldownField.addActionListener(e -> savePreviewCooldown());
        previewCooldownField.addFocusListener(new java.awt.event.FocusAdapter() {
            @Override
            public void focusLost(java.awt.event.FocusEvent e) {
                savePreviewCooldown();
            }
        });
        bottomPanel.add(previewCooldownField);

        // 套装特效消息强制禁用开关
        this.forceDisableMessagesCheckBox = new JCheckBox("强制禁用套装消息");
        this.forceDisableMessagesCheckBox.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        this.forceDisableMessagesCheckBox
                .setSelected(Cuilian.config.getBoolean("suit_effect_messages.force_disabled", false));
        this.forceDisableMessagesCheckBox.addActionListener(e -> {
            boolean forceDisabled = forceDisableMessagesCheckBox.isSelected();
            try {
                Cuilian instance = Cuilian.getInstance();
                if (instance != null) {
                    instance.getConfig().set("suit_effect_messages.force_disabled", forceDisabled);
                    instance.saveConfig();
                    Cuilian.config.set("suit_effect_messages.force_disabled", forceDisabled);

                    String message = forceDisabled ? "套装特效消息已强制禁用！玩家无法在UI中开启消息。" : "套装特效消息强制禁用已关闭！玩家可以在UI中控制消息显示。";
                    JOptionPane.showMessageDialog(null, message);

                    // 延迟通知所有打开特效设置界面的玩家更新界面
                    if (org.bukkit.Bukkit.getServer() != null) {
                        org.bukkit.Bukkit.getScheduler().runTaskLater(
                                cn.winde.cuilian.Cuilian.getInstance(), () -> {
                                    System.out.println("[Mygui] 强制禁用开关状态改变，通知界面更新: " + forceDisabled);
                                    cn.winde.cuilian.gui.InGameGUIListener.notifyConfigReload();
                                }, 1L);
                    }
                }
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(null, "保存配置失败: " + ex.getMessage());
                ex.printStackTrace();
            }
        });
        bottomPanel.add(this.forceDisableMessagesCheckBox);

        // 玩家装备预览按钮
        JButton playerPreviewBtn = new JButton("玩家装备预览");
        playerPreviewBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        playerPreviewBtn.addActionListener(e -> openPlayerEquipmentPreview());
        bottomPanel.add(playerPreviewBtn);

        // 游戏风格装备预览按钮
        JButton gameStylePreviewBtn = new JButton("游戏风格预览");
        gameStylePreviewBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        gameStylePreviewBtn.setBackground(new java.awt.Color(72, 61, 139));
        gameStylePreviewBtn.setForeground(java.awt.Color.WHITE);
        gameStylePreviewBtn.setFocusPainted(false);
        gameStylePreviewBtn.setToolTipText("直接打开游戏风格的装备预览界面，显示详细特效信息");
        gameStylePreviewBtn.addActionListener(e -> openGameStyleEquipmentPreview());
        bottomPanel.add(gameStylePreviewBtn);

        // 重载按钮
        JButton reloadBtn = new JButton("重载配置");
        reloadBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        reloadBtn.addActionListener(e -> {
            try {
                // 直接重新加载配置文件
                reloadAllConfigs();
                JOptionPane.showMessageDialog(null, "配置文件重载成功！");
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(null, "配置文件重载失败: " + ex.getMessage());
                ex.printStackTrace();
            }
        });
        bottomPanel.add(reloadBtn);

        // 重载贴图按钮
        JButton reloadTexturesBtn = new JButton("重载贴图");
        reloadTexturesBtn.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
        reloadTexturesBtn.setBackground(new java.awt.Color(34, 139, 34));
        reloadTexturesBtn.setForeground(java.awt.Color.WHITE);
        reloadTexturesBtn.setFocusPainted(false);
        reloadTexturesBtn.setToolTipText("重新扫描和加载装备贴图文件");
        reloadTexturesBtn.addActionListener(e -> {
            try {
                cn.winde.cuilian.texture.TextureManager.reloadTextures();
                String stats = cn.winde.cuilian.texture.TextureManager.getStatistics();
                JOptionPane.showMessageDialog(null, "装备贴图重载成功！\n" + stats);
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(null, "装备贴图重载失败: " + ex.getMessage());
                ex.printStackTrace();
            }
        });
        bottomPanel.add(reloadTexturesBtn);

        return bottomPanel;
    }

    /**
     * 打开玩家装备预览窗口
     */
    private void openPlayerEquipmentPreview() {
        // 创建玩家选择对话框
        PlayerSelectionDialog selectionDialog = new PlayerSelectionDialog(this);
        selectionDialog.setVisible(true);

        String selectedPlayer = selectionDialog.getSelectedPlayer();
        if (selectedPlayer == null || selectedPlayer.trim().isEmpty()) {
            return;
        }

        // 检查玩家是否在线
        org.bukkit.entity.Player onlinePlayer = org.bukkit.Bukkit.getPlayer(selectedPlayer);

        if (onlinePlayer != null && onlinePlayer.isOnline()) {
            // 在线玩家 - 使用实时数据
            PlayerEquipmentPreviewDialog dialog = new PlayerEquipmentPreviewDialog(this, onlinePlayer, true);
            dialog.setVisible(true);
        } else {
            // 离线玩家 - 使用离线数据
            org.bukkit.OfflinePlayer offlinePlayer = org.bukkit.Bukkit.getOfflinePlayer(selectedPlayer);
            if (offlinePlayer.hasPlayedBefore()) {
                PlayerEquipmentPreviewDialog dialog = new PlayerEquipmentPreviewDialog(this, offlinePlayer);
                dialog.setVisible(true);
            } else {
                JOptionPane.showMessageDialog(this,
                        "玩家 " + selectedPlayer + " 从未进入过服务器！",
                        "错误",
                        JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * 打开游戏风格装备预览窗口
     */
    private void openGameStyleEquipmentPreview() {
        // 创建玩家选择对话框
        PlayerSelectionDialog selectionDialog = new PlayerSelectionDialog(this);
        selectionDialog.setVisible(true);

        String selectedPlayer = selectionDialog.getSelectedPlayer();
        if (selectedPlayer == null || selectedPlayer.trim().isEmpty()) {
            return;
        }

        // 检查玩家是否在线
        org.bukkit.entity.Player onlinePlayer = org.bukkit.Bukkit.getPlayer(selectedPlayer);

        if (onlinePlayer != null && onlinePlayer.isOnline()) {
            // 在线玩家 - 直接打开游戏风格预览
            GameStyleEquipmentPreview gamePreview = new GameStyleEquipmentPreview(this, onlinePlayer);
            gamePreview.setVisible(true);
        } else {
            // 离线玩家 - 使用离线数据
            org.bukkit.OfflinePlayer offlinePlayer = org.bukkit.Bukkit.getOfflinePlayer(selectedPlayer);
            if (offlinePlayer.hasPlayedBefore()) {
                GameStyleEquipmentPreview gamePreview = new GameStyleEquipmentPreview(this, offlinePlayer);
                gamePreview.setVisible(true);
            } else {
                JOptionPane.showMessageDialog(this,
                        "玩家 " + selectedPlayer + " 从未进入过服务器！",
                        "错误",
                        JOptionPane.ERROR_MESSAGE);
            }
        }
    }

    /**
     * 初始化数据
     */
    private void initializeData() {
        // 添加窗口监听器，确保在窗口显示时刷新在线玩家列表
        this.addWindowListener(new java.awt.event.WindowAdapter() {
            @Override
            public void windowOpened(java.awt.event.WindowEvent e) {
                // 窗口打开时，延迟刷新在线玩家列表
                javax.swing.SwingUtilities.invokeLater(() -> {
                    if (charmOnlinePlayersModel != null) {
                        // 重新初始化符咒在线玩家列表
                        initializeCharmOnlinePlayersList();
                    }
                });
            }

            @Override
            public void windowActivated(java.awt.event.WindowEvent e) {
                // 窗口激活时，也刷新一次在线玩家列表
                if (charmOnlinePlayersModel != null && charmOnlinePlayersModel.getSize() == 0) {
                    // 如果列表为空，重新加载
                    javax.swing.SwingUtilities.invokeLater(() -> {
                        refreshCharmOnlinePlayersList();
                    });
                }
            }
        });
    }

    /**
     * 多列玩家网格面板，支持自动换列和滚动
     */
    private static class PlayerGridPanel extends JPanel {
        private static final int PLAYER_BUTTON_WIDTH = 130;
        private static final int PLAYER_BUTTON_HEIGHT = 32; // 固定按钮高度，适应28px头颅
        private static final int SPACING = 5;

        private final java.util.List<PlayerButton> playerButtons = new java.util.ArrayList<>();
        private String selectedPlayer = null;

        public PlayerGridPanel() {
            // 使用null布局，手动控制组件位置以实现列优先排列
            setLayout(null);
            setBackground(java.awt.Color.WHITE);
            setBorder(BorderFactory.createEmptyBorder(5, 5, 5, 5));
        }

        /**
         * 更新玩家列表
         */
        public void updatePlayers(java.util.List<String> players) {
            // 清除现有按钮
            playerButtons.clear();
            selectedPlayer = null;

            // 创建新的玩家按钮
            for (String playerName : players) {
                PlayerButton playerButton = new PlayerButton(playerName);
                playerButtons.add(playerButton);
            }

            // 重新布局所有按钮
            relayoutButtons();
        }

        /**
         * 获取选中的玩家
         */
        public String getSelectedPlayer() {
            return selectedPlayer;
        }

        /**
         * 清除选择
         */
        public void clearSelection() {
            selectedPlayer = null;
            for (PlayerButton button : playerButtons) {
                button.setSelected(false);
            }
        }

        /**
         * 添加单个玩家到列表
         */
        public void addPlayer(String playerName) {
            // 检查玩家是否已存在
            for (PlayerButton button : playerButtons) {
                if (button.playerName.equals(playerName)) {
                    return; // 玩家已存在，不重复添加
                }
            }

            // 创建新的玩家按钮
            PlayerButton playerButton = new PlayerButton(playerName);
            playerButtons.add(playerButton);

            // 重新布局所有按钮
            relayoutButtons();
        }

        /**
         * 从列表中移除单个玩家
         */
        public void removePlayer(String playerName) {
            // 查找并移除玩家按钮
            PlayerButton toRemove = null;
            for (PlayerButton button : playerButtons) {
                if (button.playerName.equals(playerName)) {
                    toRemove = button;
                    break;
                }
            }

            if (toRemove != null) {
                // 从列表和面板中移除
                playerButtons.remove(toRemove);
                remove(toRemove);

                // 如果移除的是当前选中的玩家，清除选择
                if (playerName.equals(selectedPlayer)) {
                    selectedPlayer = null;
                }

                // 重新布局剩余按钮
                relayoutButtons();
            }
        }

        /**
         * 重新布局所有按钮
         */
        private void relayoutButtons() {
            // 清除所有组件
            removeAll();

            if (playerButtons.isEmpty()) {
                // 显示无玩家在线的提示
                JLabel noPlayersLabel = new JLabel("暂无在线玩家", JLabel.CENTER);
                noPlayersLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 14));
                noPlayersLabel.setForeground(java.awt.Color.GRAY);
                noPlayersLabel.setBounds(50, 100, 200, 30);
                add(noPlayersLabel);

                // 设置面板的首选大小
                setPreferredSize(new java.awt.Dimension(280, 580));
            } else {
                // 按列优先排列玩家按钮，基于底部边界自动换列
                int currentColumn = 0;
                int currentY = SPACING;

                for (PlayerButton playerButton : playerButtons) {
                    // 检查当前列是否已经有16个按钮
                    // 计算当前列的按钮数量
                    int buttonsInCurrentColumn = (currentY - SPACING) / (PLAYER_BUTTON_HEIGHT + SPACING);
                    if (buttonsInCurrentColumn >= 16) {
                        // 换到下一列
                        currentColumn++;
                        currentY = SPACING;
                    }

                    // 计算实际坐标
                    int x = SPACING + currentColumn * (PLAYER_BUTTON_WIDTH + SPACING);
                    int y = currentY;

                    // 设置按钮位置和大小
                    playerButton.setBounds(x, y, PLAYER_BUTTON_WIDTH, PLAYER_BUTTON_HEIGHT);
                    add(playerButton);

                    // 更新下一个按钮的Y坐标
                    currentY += PLAYER_BUTTON_HEIGHT + SPACING;
                }

                // 计算并设置面板的首选大小，支持水平滚动
                int totalWidth = SPACING + (currentColumn + 1) * (PLAYER_BUTTON_WIDTH + SPACING);
                setPreferredSize(new java.awt.Dimension(totalWidth, 580));
            }

            revalidate();
            repaint();
        }

        /**
         * 玩家按钮类
         */
        private class PlayerButton extends JPanel {
            private final String playerName;
            private boolean selected = false;
            private ImageIcon headIcon = null;

            public PlayerButton(String playerName) {
                this.playerName = playerName;
                setLayout(new java.awt.FlowLayout(java.awt.FlowLayout.LEFT, 4, 2));
                setBorder(BorderFactory.createRaisedBevelBorder());
                setBackground(java.awt.Color.WHITE);
                setCursor(java.awt.Cursor.getPredefinedCursor(java.awt.Cursor.HAND_CURSOR));

                // 异步加载玩家头颅
                loadPlayerHead();

                // 添加点击事件
                addMouseListener(new MouseAdapter() {
                    @Override
                    public void mouseClicked(MouseEvent e) {
                        if (e.getClickCount() == 1) {
                            // 单击切换选择状态
                            toggleSelection();
                        }
                    }

                    @Override
                    public void mouseEntered(MouseEvent e) {
                        if (!selected) {
                            setBackground(new java.awt.Color(230, 230, 230));
                        }
                    }

                    @Override
                    public void mouseExited(MouseEvent e) {
                        if (!selected) {
                            setBackground(java.awt.Color.WHITE);
                        }
                    }
                });

                updateDisplay();
            }

            private void toggleSelection() {
                if (selected) {
                    // 如果当前已选中，则取消选择
                    setSelected(false);
                    selectedPlayer = null;

                    // 清空离线玩家输入框
                    if (instance != null && instance.offlinePlayerField != null) {
                        instance.offlinePlayerField.setText("");
                    }
                    // 清空符咒管理的离线玩家输入框
                    if (instance != null && instance.charmTargetPlayerField != null) {
                        instance.charmTargetPlayerField.setText("");
                    }
                } else {
                    // 如果当前未选中，则选中当前玩家
                    selectPlayer();
                }
            }

            private void selectPlayer() {
                // 清除其他按钮的选择状态
                for (PlayerButton button : playerButtons) {
                    button.setSelected(false);
                }

                // 设置当前按钮为选中状态
                setSelected(true);
                selectedPlayer = playerName;

                // 更新玩家管理的离线玩家输入框
                if (instance != null && instance.offlinePlayerField != null) {
                    instance.offlinePlayerField.setText(playerName);
                }
                // 更新符咒管理的离线玩家输入框
                if (instance != null && instance.charmTargetPlayerField != null) {
                    instance.charmTargetPlayerField.setText(playerName);
                }
            }

            public void setSelected(boolean selected) {
                this.selected = selected;
                if (selected) {
                    setBackground(new java.awt.Color(173, 216, 230)); // 浅蓝色
                    setBorder(BorderFactory.createLoweredBevelBorder());
                } else {
                    setBackground(java.awt.Color.WHITE);
                    setBorder(BorderFactory.createRaisedBevelBorder());
                }
            }

            private void loadPlayerHead() {
                // 异步加载玩家头颅
                CompletableFuture.supplyAsync(() -> {
                    try {
                        // 使用 minotar.net 网站通过玩家名查询皮肤
                        String url = "https://minotar.net/avatar/" + playerName + "/28";
                        URL imageUrl = new URL(url);
                        BufferedImage image = ImageIO.read(imageUrl);
                        if (image != null) {
                            Image scaledImage = image.getScaledInstance(28, 28, Image.SCALE_SMOOTH);
                            return new ImageIcon(scaledImage);
                        }
                    } catch (Exception e) {
                        // 网络错误时使用默认头颅
                        System.out.println("加载玩家头颅失败: " + playerName + " - " + e.getMessage());
                    }
                    return createDefaultHead();
                }).thenAccept(icon -> {
                    javax.swing.SwingUtilities.invokeLater(() -> {
                        headIcon = icon;
                        updateDisplay();
                    });
                });
            }

            private ImageIcon createDefaultHead() {
                try {
                    BufferedImage defaultHead = new BufferedImage(28, 28, BufferedImage.TYPE_INT_ARGB);
                    Graphics2D g2d = defaultHead.createGraphics();
                    g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

                    // 绘制简单的头颅形状
                    g2d.setColor(new Color(139, 69, 19)); // 棕色皮肤
                    g2d.fillRect(0, 0, 28, 28);

                    // 绘制眼睛
                    g2d.setColor(Color.BLACK);
                    g2d.fillRect(7, 8, 3, 3);
                    g2d.fillRect(18, 8, 3, 3);

                    // 绘制嘴巴
                    g2d.fillRect(11, 18, 6, 2);

                    g2d.dispose();
                    return new ImageIcon(defaultHead);
                } catch (Exception e) {
                    return null;
                }
            }

            private void updateDisplay() {
                removeAll();

                // 添加头颅图标
                if (headIcon != null) {
                    JLabel iconLabel = new JLabel(headIcon);
                    add(iconLabel);
                }

                // 添加玩家名称
                JLabel nameLabel = new JLabel(playerName);
                nameLabel.setFont(new java.awt.Font("微软雅黑", java.awt.Font.PLAIN, 12));
                add(nameLabel);

                revalidate();
                repaint();
            }
        }
    }

    /**
     * 清空当前星级的所有特效
     */
    private void clearAllEffectsForLevel() {
        String selectedLevel = (String) this.effectStarLevelComboBox.getSelectedItem();
        if (selectedLevel == null || selectedLevel.equals("选择星级")) {
            JOptionPane.showMessageDialog(null, "请先选择星级！");
            return;
        }

        int result = JOptionPane.showConfirmDialog(null,
                "确定要清空星级 " + selectedLevel + " 的所有特效吗？\n此操作会立即保存到配置文件！",
                "确认清空",
                JOptionPane.YES_NO_OPTION);

        if (result == JOptionPane.YES_OPTION) {
            try {
                // 清空界面列表
                this.currentEffectsModel.clear();

                // 立即保存到配置文件
                String levelStr = selectedLevel.replace("星", "");
                String configPath = "eff.leve" + levelStr + ".effects";

                // 设置为空列表
                Cuilian.config.set(configPath, new java.util.ArrayList<>());

                // 保存配置文件
                Cuilian.config.save(Cuilian.filess);

                this.statusLabel.setText("已清空星级 " + selectedLevel + " 的所有特效并保存到配置文件");
                this.statusLabel.setForeground(new java.awt.Color(0, 128, 0));

                JOptionPane.showMessageDialog(null, "星级 " + selectedLevel + " 的特效已清空并保存到配置文件！");

            } catch (Exception e) {
                this.statusLabel.setText("清空特效失败: " + e.getMessage());
                this.statusLabel.setForeground(new java.awt.Color(255, 0, 0));
                JOptionPane.showMessageDialog(null, "清空特效失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * 保存所有特效配置
     */
    private void saveAllEffectConfigurations() {
        try {
            // 保存当前星级的特效配置到config.yml
            String selectedLevel = (String) this.effectStarLevelComboBox.getSelectedItem();
            if (selectedLevel == null || selectedLevel.equals("选择星级")) {
                JOptionPane.showMessageDialog(null, "请先选择要保存的星级！");
                return;
            }

            // 提取星级数字
            String levelStr = selectedLevel.replace("星", "");

            // 收集当前特效列表的数据
            java.util.List<java.util.Map<String, Object>> effectsList = new java.util.ArrayList<>();

            for (int i = 0; i < this.currentEffectsModel.getSize(); i++) {
                String effectText = this.currentEffectsModel.getElementAt(i);

                // 解析特效文本: "type - 中文 [启用/禁用] (color1,color2,color3)"
                String[] parts = effectText.split(" \\[");
                if (parts.length >= 2) {
                    String typeDisplay = parts[0];
                    // 提取英文部分作为实际的特效类型
                    String type = typeDisplay.split(" - ")[0];

                    String[] statusAndColors = parts[1].split("\\] \\(");
                    if (statusAndColors.length >= 2) {
                        boolean enabled = statusAndColors[0].equals("启用");
                        String colorsStr = statusAndColors[1].replace(")", "");
                        String[] colors = colorsStr.split(",");

                        java.util.Map<String, Object> effectMap = new java.util.HashMap<>();
                        effectMap.put("type", type);
                        effectMap.put("enabled", enabled);
                        effectMap.put("colore1", colors.length > 0 ? colors[0] : "红");
                        effectMap.put("colore2", colors.length > 1 ? colors[1] : "橙");
                        effectMap.put("colore3", colors.length > 2 ? colors[2] : "黄");

                        effectsList.add(effectMap);
                    }
                }
            }

            // 保存到配置文件
            String configPath = "eff.leve" + levelStr + ".effects";
            Cuilian.config.set(configPath, effectsList);

            // 保存文件
            Cuilian.config.save(Cuilian.filess);

            this.statusLabel.setText("特效配置保存成功！");
            this.statusLabel.setForeground(new java.awt.Color(0, 128, 0));
            JOptionPane.showMessageDialog(null, "星级 " + selectedLevel + " 的特效配置已保存到config.yml！");
        } catch (Exception e) {
            this.statusLabel.setText("保存失败: " + e.getMessage());
            this.statusLabel.setForeground(new java.awt.Color(255, 0, 0));
            JOptionPane.showMessageDialog(null, "保存特效配置失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建新星级
     */
    private void createNewStarLevel() {
        String levelText = this.newStarLevelField.getText().trim();
        if (levelText.isEmpty()) {
            JOptionPane.showMessageDialog(null, "请输入星级数字！");
            return;
        }

        try {
            int level = Integer.parseInt(levelText);
            if (level <= 0) {
                JOptionPane.showMessageDialog(null, "星级必须是正整数！");
                return;
            }

            // 检查星级是否已存在
            String configPath = "eff.leve" + level;
            if (Cuilian.config.contains(configPath)) {
                int result = JOptionPane.showConfirmDialog(null,
                        "星级 " + level + " 已存在，是否覆盖？",
                        "确认覆盖",
                        JOptionPane.YES_NO_OPTION);
                if (result != JOptionPane.YES_OPTION) {
                    return;
                }
            }

            // 创建新星级配置
            Cuilian.config.set(configPath + ".effects", new java.util.ArrayList<>());

            // 保存配置文件
            Cuilian.config.save(Cuilian.filess);

            // 更新星级下拉框
            refreshStarLevelComboBox();

            // 选择新创建的星级
            this.effectStarLevelComboBox.setSelectedItem(level + "星");

            // 清空输入框
            this.newStarLevelField.setText("");

            this.statusLabel.setText("星级 " + level + " 创建成功！");
            this.statusLabel.setForeground(new java.awt.Color(0, 128, 0));
            JOptionPane.showMessageDialog(null, "星级 " + level + " 创建成功！");

        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(null, "请输入有效的数字！");
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, "创建星级失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 删除选中的星级
     */
    private void deleteSelectedStarLevel() {
        String selectedLevel = (String) this.effectStarLevelComboBox.getSelectedItem();
        if (selectedLevel == null || selectedLevel.equals("选择星级")) {
            JOptionPane.showMessageDialog(null, "请先选择要删除的星级！");
            return;
        }

        int result = JOptionPane.showConfirmDialog(null,
                "确定要删除星级 " + selectedLevel + " 及其所有特效配置吗？\n此操作不可撤销！",
                "确认删除",
                JOptionPane.YES_NO_OPTION,
                JOptionPane.WARNING_MESSAGE);

        if (result == JOptionPane.YES_OPTION) {
            try {
                // 提取星级数字
                String levelStr = selectedLevel.replace("星", "");
                String configPath = "eff.leve" + levelStr;

                // 从配置文件中删除
                Cuilian.config.set(configPath, null);

                // 保存配置文件
                Cuilian.config.save(Cuilian.filess);

                // 更新星级下拉框
                refreshStarLevelComboBox();

                // 清空特效列表
                this.currentEffectsModel.clear();

                this.statusLabel.setText("星级 " + selectedLevel + " 删除成功！");
                this.statusLabel.setForeground(new java.awt.Color(0, 128, 0));
                JOptionPane.showMessageDialog(null, "星级 " + selectedLevel + " 删除成功！");

            } catch (Exception e) {
                JOptionPane.showMessageDialog(null, "删除星级失败: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * 刷新星级下拉框
     */
    private void refreshStarLevelComboBox() {
        this.effectStarLevelComboBox.removeAllItems();
        this.effectStarLevelComboBox.addItem("选择星级");

        // 动态读取已配置的星级
        try {
            if (Cuilian.config != null && Cuilian.config.isConfigurationSection("eff")) {
                org.bukkit.configuration.ConfigurationSection effSection = Cuilian.config
                        .getConfigurationSection("eff");
                java.util.Set<String> levelKeys = effSection.getKeys(false);

                // 提取数字并排序
                java.util.List<Integer> levelNumbers = new java.util.ArrayList<>();
                for (String key : levelKeys) {
                    if (key.startsWith("leve")) {
                        try {
                            int level = Integer.parseInt(key.substring(4));
                            levelNumbers.add(level);
                        } catch (NumberFormatException e) {
                            // 忽略无效的星级配置
                        }
                    }
                }

                // 排序并添加到下拉框
                java.util.Collections.sort(levelNumbers);
                for (Integer level : levelNumbers) {
                    this.effectStarLevelComboBox.addItem(level + "星");
                }
            }
        } catch (Exception e) {
            System.out.println("刷新星级列表时出错: " + e.getMessage());
        }
    }
}