package cn.winde.baoshi;

import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

/**
 * 宝石相关功能类
 */
public class baoshi {
    
    /**
     * 从玩家背包中扣除指定物品
     * 
     * @param player 玩家
     * @param item 要扣除的物品
     * @return 是否成功扣除
     */
    public static boolean TakePlayerItem(Player player, ItemStack item) {
        if (player == null || item == null) {
            return false;
        }
        
        // 检查玩家背包中是否有足够的物品
        if (player.getInventory().containsAtLeast(item, 1)) {
            // 从玩家背包中移除物品
            player.getInventory().removeItem(item);
            return true;
        }
        
        return false;
    }
}
