#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除1.8.8版本中明确不支持的贴图文件（保守版本）
只删除100%确定在1.8.8中不存在的物品
"""

import os
from pathlib import Path

def delete_definitely_unused_textures():
    """删除1.8.8版本中明确不支持的贴图文件"""
    texture_dir = Path("src/main/resources/textures")
    
    if not texture_dir.exists():
        print(f"贴图目录不存在: {texture_dir}")
        return
    
    # 100%确定在1.8.8中不存在的文件
    definitely_unused = [
        # 1.9+引入的物品（100%确定）
        "shield.png",
        "elytra.png", 
        "broken_elytra.png",
        "end_crystal.png",
        "totem_of_undying.png",
        "spectral_arrow.png",
        "tipped_arrow_base.png",
        "tipped_arrow_head.png",
        "lingering_potion.png",
        "splash_potion.png",
        "dragon_breath.png",
        "chorus_fruit.png",
        "popped_chorus_fruit.png",
        "beetroot.png",
        "beetroot_seeds.png", 
        "beetroot_soup.png",
        
        # 1.13+引入的物品（100%确定）
        "turtle_helmet.png",
        "turtle_egg.png",
        "heart_of_the_sea.png",
        "nautilus_shell.png",
        "phantom_membrane.png",
        "trident.png",
        "kelp.png",
        "dried_kelp.png",
        "seagrass.png",
        "sea_pickle.png",
        "cod.png",
        "salmon.png",
        "tropical_fish.png",
        "pufferfish.png",
        "cod_bucket.png",
        "salmon_bucket.png",
        "tropical_fish_bucket.png",
        "pufferfish_bucket.png",
        "scute.png",
        
        # 1.14+引入的物品（100%确定）
        "crossbow_standby.png",
        "crossbow_arrow.png", 
        "crossbow_firework.png",
        "crossbow_pulling_0.png",
        "crossbow_pulling_1.png",
        "crossbow_pulling_2.png",
        "suspicious_stew.png",
        "sweet_berries.png",
        "honeycomb.png",
        "honey_bottle.png",
        "campfire.png",
        "soul_campfire.png",
        "lantern.png",
        "soul_lantern.png",
        "bell.png",
        "bamboo.png",
        
        # 1.16+引入的下界合金装备（100%确定）
        "netherite_helmet.png",
        "netherite_chestplate.png",
        "netherite_leggings.png",
        "netherite_boots.png",
        "netherite_sword.png",
        "netherite_pickaxe.png",
        "netherite_axe.png",
        "netherite_shovel.png",
        "netherite_hoe.png",
        "netherite_ingot.png",
        "netherite_scrap.png",
        "chain.png",
        "warped_fungus_on_a_stick.png",
        "nether_sprouts.png",
        "crimson_door.png",
        "warped_door.png",
        "crimson_sign.png",
        "warped_sign.png",
        
        # 1.9+的新木材类型的门、告示牌、船（100%确定）
        "acacia_boat.png",
        "acacia_door.png",
        "acacia_sign.png",
        "birch_boat.png",
        "birch_door.png",
        "birch_sign.png",
        "dark_oak_boat.png",
        "dark_oak_door.png",
        "dark_oak_sign.png",
        "jungle_boat.png",
        "jungle_door.png",
        "jungle_sign.png",
        "spruce_boat.png",
        "spruce_door.png",
        "spruce_sign.png",
        "oak_boat.png",
        "oak_door.png",
        "oak_sign.png",
        
        # 1.14+重命名的染料（100%确定，1.8.8中这些是INK_SACK的数据值）
        "black_dye.png",
        "blue_dye.png",
        "brown_dye.png",
        "cyan_dye.png",
        "gray_dye.png",
        "green_dye.png",
        "light_blue_dye.png",
        "light_gray_dye.png",
        "lime_dye.png",
        "magenta_dye.png",
        "orange_dye.png",
        "pink_dye.png",
        "purple_dye.png",
        "red_dye.png",
        "white_dye.png",
        "yellow_dye.png",
        
        # 1.16+音乐唱片
        "music_disc_pigstep.png",
        
        # 1.11+物品
        "shulker_shell.png",
        "iron_nugget.png",
        
        # 1.12+物品
        "knowledge_book.png",
        
        # 1.10+物品
        "structure_void.png",
        
        # 从未存在的物品
        "ruby.png",  # 从未在正式版本中存在
        
        # 1.8中不存在的动物产品
        "mutton.png",               # 1.8中不存在羊肉
        "cooked_mutton.png",        # 1.8中不存在熟羊肉
        "rabbit.png",               # 1.8中不存在兔子
        "cooked_rabbit.png",        # 1.8中不存在熟兔肉
        "rabbit_foot.png",          # 1.8中不存在兔子脚
        "rabbit_hide.png",          # 1.8中不存在兔子皮
        "rabbit_stew.png",          # 1.8中不存在兔肉煲
        
        # UI相关的新版本文件
        "empty_armor_slot_shield.png",  # 1.9+才有盾牌槽
        "bow_pulling_0.png",            # 1.8中可能不支持拉弓动画贴图
        "bow_pulling_1.png",
        "bow_pulling_2.png",
        "fishing_rod_cast.png",         # 1.8中可能不支持钓鱼竿投掷动画
        "potion_overlay.png",           # 新版本的药水覆盖层
        "spawn_egg.png",                # 1.8中刷怪蛋可能不是独立物品
        "spawn_egg_overlay.png",
        "leather_boots_overlay.png",    # 皮革装备染色覆盖层可能是新版本的
        "leather_chestplate_overlay.png",
        "leather_helmet_overlay.png",
        "leather_leggings_overlay.png",
        "filled_map_markings.png",      # 地图标记覆盖层
        "firework_rocket.png",          # 1.8中烟花可能不是独立物品
        "firework_star.png",
        "firework_star_overlay.png",
    ]
    
    deleted_count = 0
    total_size = 0
    
    print("开始删除1.8.8版本中明确不支持的贴图文件...")
    print("这是保守版本，只删除100%确定不支持的文件")
    print("=" * 60)
    
    for filename in definitely_unused:
        file_path = texture_dir / filename
        if file_path.exists():
            try:
                size = file_path.stat().st_size
                total_size += size
                file_path.unlink()
                deleted_count += 1
                print(f"✓ 删除: {filename} ({size} bytes)")
            except Exception as e:
                print(f"✗ 删除失败: {filename} - {e}")
        else:
            print(f"- 文件不存在: {filename}")
    
    print("=" * 60)
    print(f"删除完成!")
    print(f"删除了 {deleted_count} 个文件")
    print(f"节省空间: {total_size} bytes ({total_size/1024:.1f} KB)")
    
    # 显示剩余的文件数量
    remaining_files = list(texture_dir.glob("*.png"))
    print(f"\n剩余PNG文件: {len(remaining_files)} 个")
    
    print("\n建议保留的1.8.8支持的重要文件包括:")
    important_files = [
        "diamond_sword.png", "iron_sword.png", "stone_sword.png", "wooden_sword.png", "golden_sword.png",
        "diamond_helmet.png", "diamond_chestplate.png", "diamond_leggings.png", "diamond_boots.png",
        "iron_helmet.png", "iron_chestplate.png", "iron_leggings.png", "iron_boots.png",
        "leather_helmet.png", "leather_chestplate.png", "leather_leggings.png", "leather_boots.png",
        "bow.png", "arrow.png", "apple.png", "bread.png", "diamond.png", "emerald.png",
        "gold_ingot.png", "iron_ingot.png", "coal.png", "redstone.png", "enchanted_book.png"
    ]
    
    for file in important_files:
        if (texture_dir / file).exists():
            print(f"  ✓ {file}")
        else:
            print(f"  ✗ {file} (缺失)")

if __name__ == "__main__":
    print("1.8.8版本贴图清理工具（保守版本）")
    print("此脚本只删除100%确定在1.8.8中不存在的贴图文件")
    print("这将安全地减小插件的大小，不会影响1.8.8的兼容性")
    print()
    
    response = input("确定要继续吗? (y/N): ").lower().strip()
    if response in ['y', 'yes', '是']:
        delete_definitely_unused_textures()
    else:
        print("操作已取消")
