#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析1.8.8版本中使用的贴图文件
"""

import os
import re
from pathlib import Path

# 1.8.8版本支持的Material枚举（基于代码分析）
MATERIALS_1_8_8 = {
    # 装备类
    'LEATHER_HELMET', 'LEATHER_CHESTPLATE', 'LEATHER_LEGGINGS', 'LEATHER_BOOTS',
    'CHAINMAIL_HELMET', 'CHAINMAIL_CHESTPLATE', 'CHAINMAIL_LEGGINGS', 'CHAINMAIL_BOOTS',
    'IRON_HELMET', 'IRON_CHESTPLATE', 'IRON_LEGGINGS', 'IRON_BOOTS',
    'DIAMOND_HELMET', 'DIAMOND_CHESTPLATE', 'DIAMOND_LEGGINGS', 'DIAMOND_BOOTS',
    'GOLD_HELMET', 'GOLD_CHESTPLATE', 'GOLD_LEGGINGS', 'GOLD_BOOTS',

    # 武器类
    'WOOD_SWORD', 'STONE_SWORD', 'IRON_SWORD', 'DIAMOND_SWORD', 'GOLD_SWORD',
    'WOOD_AXE', 'STONE_AXE', 'IRON_AXE', 'DIAMOND_AXE', 'GOLD_AXE',
    'WOOD_PICKAXE', 'STONE_PICKAXE', 'IRON_PICKAXE', 'DIAMOND_PICKAXE', 'GOLD_PICKAXE',
    'WOOD_SPADE', 'STONE_SPADE', 'IRON_SPADE', 'DIAMOND_SPADE', 'GOLD_SPADE',
    'WOOD_HOE', 'STONE_HOE', 'IRON_HOE', 'DIAMOND_HOE', 'GOLD_HOE',
    'BOW', 'FISHING_ROD', 'FLINT_AND_STEEL', 'SHEARS',

    # 食物类
    'APPLE', 'BREAD', 'COOKED_BEEF', 'COOKED_CHICKEN', 'COOKED_FISH', 'GOLDEN_APPLE',
    'GOLDEN_CARROT', 'CAKE', 'COOKIE', 'MELON', 'CARROT_ITEM', 'POTATO_ITEM', 'BAKED_POTATO',
    'PORK', 'GRILLED_PORK', 'RAW_FISH', 'COOKED_FISH', 'MUSHROOM_SOUP', 'ROTTEN_FLESH',
    'SPIDER_EYE', 'FERMENTED_SPIDER_EYE', 'MAGMA_CREAM', 'GLISTERING_MELON',

    # 材料类
    'DIAMOND', 'EMERALD', 'GOLD_INGOT', 'IRON_INGOT', 'COAL', 'REDSTONE', 'INK_SACK',
    'QUARTZ', 'STICK', 'STRING', 'FEATHER', 'LEATHER', 'PAPER', 'BOOK', 'SLIME_BALL',
    'ENDER_PEARL', 'BLAZE_ROD', 'GHAST_TEAR', 'NETHER_STAR', 'GOLD_NUGGET', 'IRON_INGOT',
    'CLAY_BALL', 'CLAY_BRICK', 'NETHER_BRICK_ITEM', 'PRISMARINE_SHARD', 'PRISMARINE_CRYSTALS',

    # 工具和实用物品
    'BUCKET', 'WATER_BUCKET', 'LAVA_BUCKET', 'MILK_BUCKET', 'COMPASS', 'WATCH',
    'MAP', 'NAME_TAG', 'SADDLE', 'ITEM_FRAME', 'FLOWER_POT_ITEM', 'CARROT_STICK',
    'EMPTY_MAP', 'SKULL_ITEM',

    # 药水和附魔
    'POTION', 'GLASS_BOTTLE', 'ENCHANTED_BOOK', 'EXP_BOTTLE', 'BREWING_STAND_ITEM',

    # 箭矢和投掷物
    'ARROW', 'SNOW_BALL', 'EGG', 'FIREBALL',

    # 其他常见物品
    'BONE', 'SUGAR_CANE', 'FLINT', 'GLOWSTONE_DUST', 'BLAZE_POWDER', 'NETHER_STALK',
    'SULPHUR', 'SUGAR', 'WHEAT', 'WHEAT_SEEDS', 'MELON_SEEDS', 'PUMPKIN_SEEDS',
    'SEEDS', 'COCOA', 'PAINTING', 'SIGN', 'WOOD_DOOR', 'IRON_DOOR_BLOCK',
    'REDSTONE_COMPARATOR', 'DIODE', 'MINECART', 'STORAGE_MINECART', 'POWERED_MINECART',
    'EXPLOSIVE_MINECART', 'HOPPER_MINECART', 'COMMAND_MINECART', 'BOAT', 'RECORD_11',
    'RECORD_13', 'RECORD_BLOCKS', 'RECORD_CAT', 'RECORD_CHIRP', 'RECORD_FAR',
    'RECORD_MALL', 'RECORD_MELLOHI', 'RECORD_STAL', 'RECORD_STRAD', 'RECORD_WAIT', 'RECORD_WARD'
}

# 1.8.8版本不支持的物品（1.9+引入的）
NOT_SUPPORTED_1_8_8 = {
    # 1.9+
    'SHIELD', 'ELYTRA', 'END_CRYSTAL', 'TOTEM_OF_UNDYING', 'SPECTRAL_ARROW', 'TIPPED_ARROW',
    'LINGERING_POTION', 'SPLASH_POTION', 'DRAGON_BREATH', 'CHORUS_FRUIT', 'POPPED_CHORUS_FRUIT',
    'BEETROOT', 'BEETROOT_SEEDS', 'BEETROOT_SOUP',

    # 1.10+
    'STRUCTURE_VOID', 'MAGMA', 'NETHER_WART_BLOCK', 'RED_NETHER_BRICK', 'BONE_BLOCK',

    # 1.11+
    'OBSERVER', 'SHULKER_SHELL', 'TOTEM', 'IRON_NUGGET',

    # 1.12+
    'KNOWLEDGE_BOOK', 'CONCRETE', 'CONCRETE_POWDER', 'GLAZED_TERRACOTTA',

    # 1.13+
    'TURTLE_HELMET', 'HEART_OF_THE_SEA', 'NAUTILUS_SHELL', 'PHANTOM_MEMBRANE',
    'TRIDENT', 'CONDUIT', 'KELP', 'DRIED_KELP', 'SEAGRASS', 'SEA_PICKLE',
    'COD', 'SALMON', 'TROPICAL_FISH', 'PUFFERFISH', 'COD_BUCKET', 'SALMON_BUCKET',
    'TROPICAL_FISH_BUCKET', 'PUFFERFISH_BUCKET', 'SCUTE',

    # 1.14+
    'CROSSBOW', 'SUSPICIOUS_STEW', 'SWEET_BERRIES', 'HONEYCOMB', 'HONEY_BOTTLE',
    'CAMPFIRE', 'LANTERN', 'BELL', 'BAMBOO', 'SCAFFOLDING',

    # 1.15+
    'HONEY_BLOCK', 'HONEYCOMB_BLOCK',

    # 1.16+
    'NETHERITE_HELMET', 'NETHERITE_CHESTPLATE', 'NETHERITE_LEGGINGS', 'NETHERITE_BOOTS',
    'NETHERITE_SWORD', 'NETHERITE_PICKAXE', 'NETHERITE_AXE', 'NETHERITE_SHOVEL', 'NETHERITE_HOE',
    'NETHERITE_INGOT', 'NETHERITE_SCRAP', 'ANCIENT_DEBRIS', 'CRYING_OBSIDIAN',
    'SOUL_CAMPFIRE', 'SOUL_LANTERN', 'CHAIN', 'WARPED_FUNGUS_ON_A_STICK',
    'NETHER_SPROUTS', 'CRIMSON_DOOR', 'WARPED_DOOR', 'CRIMSON_SIGN', 'WARPED_SIGN',

    # 各种新版本的门、告示牌、船等
    'ACACIA_BOAT', 'ACACIA_DOOR', 'ACACIA_SIGN', 'BIRCH_BOAT', 'BIRCH_DOOR', 'BIRCH_SIGN',
    'DARK_OAK_BOAT', 'DARK_OAK_DOOR', 'DARK_OAK_SIGN', 'JUNGLE_BOAT', 'JUNGLE_DOOR', 'JUNGLE_SIGN',
    'SPRUCE_BOAT', 'SPRUCE_DOOR', 'SPRUCE_SIGN', 'OAK_BOAT', 'OAK_DOOR', 'OAK_SIGN',

    # 各种染料（1.14+重命名）
    'BLACK_DYE', 'BLUE_DYE', 'BROWN_DYE', 'CYAN_DYE', 'GRAY_DYE', 'GREEN_DYE',
    'LIGHT_BLUE_DYE', 'LIGHT_GRAY_DYE', 'LIME_DYE', 'MAGENTA_DYE', 'ORANGE_DYE',
    'PINK_DYE', 'PURPLE_DYE', 'RED_DYE', 'WHITE_DYE', 'YELLOW_DYE',

    # 各种旗帜图案
    'CREEPER_BANNER_PATTERN', 'FLOWER_BANNER_PATTERN', 'GLOBE_BANNER_PATTERN',
    'MOJANG_BANNER_PATTERN', 'PIGLIN_BANNER_PATTERN', 'SKULL_BANNER_PATTERN',

    # 其他新版本物品
    'ARMOR_STAND', 'BARRIER', 'COMMAND_BLOCK_MINECART', 'COMPARATOR', 'REPEATER',
    'HOPPER', 'CAULDRON', 'BREWING_STAND', 'EMPTY_ARMOR_SLOT_BOOTS',
    'EMPTY_ARMOR_SLOT_CHESTPLATE', 'EMPTY_ARMOR_SLOT_HELMET', 'EMPTY_ARMOR_SLOT_LEGGINGS',
    'EMPTY_ARMOR_SLOT_SHIELD', 'ENCHANTED_ITEM_GLINT', 'FILLED_MAP_MARKINGS',
    'FIREWORK_ROCKET', 'FIREWORK_STAR', 'FIREWORK_STAR_OVERLAY', 'FISHING_ROD_CAST',
    'BOW_PULLING_0', 'BOW_PULLING_1', 'BOW_PULLING_2', 'CROSSBOW_ARROW', 'CROSSBOW_FIREWORK',
    'CROSSBOW_PULLING_0', 'CROSSBOW_PULLING_1', 'CROSSBOW_PULLING_2', 'CROSSBOW_STANDBY',
    'TIPPED_ARROW_BASE', 'TIPPED_ARROW_HEAD', 'POTION_OVERLAY', 'SPAWN_EGG', 'SPAWN_EGG_OVERLAY',
    'LEATHER_BOOTS_OVERLAY', 'LEATHER_CHESTPLATE_OVERLAY', 'LEATHER_HELMET_OVERLAY',
    'LEATHER_LEGGINGS_OVERLAY', 'LEATHER_HORSE_ARMOR', 'IRON_HORSE_ARMOR', 'GOLDEN_HORSE_ARMOR',
    'DIAMOND_HORSE_ARMOR', 'LEAD', 'MUTTON', 'COOKED_MUTTON', 'RABBIT', 'COOKED_RABBIT',
    'RABBIT_FOOT', 'RABBIT_HIDE', 'RABBIT_STEW', 'POISONOUS_POTATO'
}

# 文件名到Material的映射（基于代码分析）
TEXTURE_MAPPINGS = {
    # 装备类
    'leather_helmet': 'LEATHER_HELMET',
    'leather_chestplate': 'LEATHER_CHESTPLATE',
    'leather_leggings': 'LEATHER_LEGGINGS',
    'leather_boots': 'LEATHER_BOOTS',
    'chainmail_helmet': 'CHAINMAIL_HELMET',
    'chainmail_chestplate': 'CHAINMAIL_CHESTPLATE',
    'chainmail_leggings': 'CHAINMAIL_LEGGINGS',
    'chainmail_boots': 'CHAINMAIL_BOOTS',
    'iron_helmet': 'IRON_HELMET',
    'iron_chestplate': 'IRON_CHESTPLATE',
    'iron_leggings': 'IRON_LEGGINGS',
    'iron_boots': 'IRON_BOOTS',
    'diamond_helmet': 'DIAMOND_HELMET',
    'diamond_chestplate': 'DIAMOND_CHESTPLATE',
    'diamond_leggings': 'DIAMOND_LEGGINGS',
    'diamond_boots': 'DIAMOND_BOOTS',
    'golden_helmet': 'GOLD_HELMET',
    'golden_chestplate': 'GOLD_CHESTPLATE',
    'golden_leggings': 'GOLD_LEGGINGS',
    'golden_boots': 'GOLD_BOOTS',

    # 武器类
    'wooden_sword': 'WOOD_SWORD',
    'stone_sword': 'STONE_SWORD',
    'iron_sword': 'IRON_SWORD',
    'diamond_sword': 'DIAMOND_SWORD',
    'golden_sword': 'GOLD_SWORD',
    'wooden_axe': 'WOOD_AXE',
    'stone_axe': 'STONE_AXE',
    'iron_axe': 'IRON_AXE',
    'diamond_axe': 'DIAMOND_AXE',
    'golden_axe': 'GOLD_AXE',
    'wooden_pickaxe': 'WOOD_PICKAXE',
    'stone_pickaxe': 'STONE_PICKAXE',
    'iron_pickaxe': 'IRON_PICKAXE',
    'diamond_pickaxe': 'DIAMOND_PICKAXE',
    'golden_pickaxe': 'GOLD_PICKAXE',
    'wooden_shovel': 'WOOD_SPADE',
    'stone_shovel': 'STONE_SPADE',
    'iron_shovel': 'IRON_SPADE',
    'diamond_shovel': 'DIAMOND_SPADE',
    'golden_shovel': 'GOLD_SPADE',
    'wooden_hoe': 'WOOD_HOE',
    'stone_hoe': 'STONE_HOE',
    'iron_hoe': 'IRON_HOE',
    'diamond_hoe': 'DIAMOND_HOE',
    'golden_hoe': 'GOLD_HOE',
    'bow': 'BOW',
    'fishing_rod': 'FISHING_ROD',
    'flint_and_steel': 'FLINT_AND_STEEL',
    'shears': 'SHEARS',

    # 食物类
    'apple': 'APPLE',
    'bread': 'BREAD',
    'cooked_beef': 'COOKED_BEEF',
    'cooked_chicken': 'COOKED_CHICKEN',
    'cooked_cod': 'COOKED_FISH',
    'golden_apple': 'GOLDEN_APPLE',
    'golden_carrot': 'GOLDEN_CARROT',
    'cake': 'CAKE',
    'cookie': 'COOKIE',
    'melon_slice': 'MELON',
    'carrot': 'CARROT_ITEM',
    'potato': 'POTATO_ITEM',
    'baked_potato': 'BAKED_POTATO',

    # 材料类
    'diamond': 'DIAMOND',
    'emerald': 'EMERALD',
    'gold_ingot': 'GOLD_INGOT',
    'iron_ingot': 'IRON_INGOT',
    'coal': 'COAL',
    'redstone': 'REDSTONE',
    'lapis_lazuli': 'INK_SACK',  # 1.8中青金石是INK_SACK的数据值
    'quartz': 'QUARTZ',
    'stick': 'STICK',
    'string': 'STRING',
    'feather': 'FEATHER',
    'leather': 'LEATHER',
    'paper': 'PAPER',
    'book': 'BOOK',
    'slime_ball': 'SLIME_BALL',
    'ender_pearl': 'ENDER_PEARL',
    'blaze_rod': 'BLAZE_ROD',
    'ghast_tear': 'GHAST_TEAR',
    'nether_star': 'NETHER_STAR',

    # 工具和实用物品
    'bucket': 'BUCKET',
    'water_bucket': 'WATER_BUCKET',
    'lava_bucket': 'LAVA_BUCKET',
    'milk_bucket': 'MILK_BUCKET',
    'compass_00': 'COMPASS',
    'clock_00': 'WATCH',
    'filled_map': 'MAP',
    'name_tag': 'NAME_TAG',
    'saddle': 'SADDLE',

    # 药水和附魔
    'potion': 'POTION',
    'glass_bottle': 'GLASS_BOTTLE',
    'enchanted_book': 'ENCHANTED_BOOK',
    'experience_bottle': 'EXP_BOTTLE',

    # 箭矢和投掷物
    'arrow': 'ARROW',
    'snowball': 'SNOW_BALL',
    'egg': 'EGG',

    # 其他
    'bone': 'BONE',
    'sugar_cane': 'SUGAR_CANE',
    'flint': 'FLINT',
    'glowstone_dust': 'GLOWSTONE_DUST',
    'blaze_powder': 'BLAZE_POWDER',
    'nether_wart': 'NETHER_STALK',
    'fire_charge': 'FIREBALL',
    'gunpowder': 'SULPHUR',
    'brick': 'CLAY_BRICK',
    'prismarine_crystals': 'PRISMARINE_CRYSTALS'
}

def analyze_textures():
    """分析贴图文件使用情况"""
    texture_dir = Path("src/main/resources/textures")

    if not texture_dir.exists():
        print(f"贴图目录不存在: {texture_dir}")
        return

    # 获取所有图片文件
    image_files = []
    for ext in ['*.png', '*.jpg', '*.jpeg', '*.gif']:
        image_files.extend(texture_dir.glob(ext))

    print(f"找到 {len(image_files)} 个图片文件")
    print("=" * 60)

    used_files = []
    unused_files = []

    for image_file in image_files:
        filename = image_file.name
        basename = filename.rsplit('.', 1)[0]  # 移除扩展名

        # 跳过特殊文件
        if basename in ['README', 'enchanted_item_glint.png']:
            continue

        # 检查是否在映射中
        if basename in TEXTURE_MAPPINGS:
            material = TEXTURE_MAPPINGS[basename]
            if material in MATERIALS_1_8_8:
                used_files.append((filename, material))
                print(f"✓ {filename} -> {material} (1.8.8支持)")
            else:
                unused_files.append((filename, f"映射到 {material} 但1.8.8不支持"))
                print(f"✗ {filename} -> {material} (1.8.8不支持)")
        else:
            # 尝试直接匹配Material名称
            material_name = basename.upper().replace('-', '_')

            # 检查是否是明确不支持的物品
            if material_name in NOT_SUPPORTED_1_8_8:
                unused_files.append((filename, f"1.8.8不支持的物品: {material_name}"))
                print(f"✗ {filename} -> {material_name} (1.8.8不支持)")
            elif material_name in MATERIALS_1_8_8:
                used_files.append((filename, material_name))
                print(f"✓ {filename} -> {material_name} (直接匹配)")
            else:
                # 检查是否是时钟或指南针的动画帧
                if basename.startswith('clock_') or basename.startswith('compass_'):
                    base_item = 'WATCH' if basename.startswith('clock_') else 'COMPASS'
                    used_files.append((filename, f"{base_item} (动画帧)"))
                    print(f"✓ {filename} -> {base_item} (动画帧)")
                else:
                    unused_files.append((filename, "无匹配的Material"))
                    print(f"✗ {filename} (无匹配的Material)")

    print("\n" + "=" * 60)
    print(f"统计结果:")
    print(f"使用的文件: {len(used_files)}")
    print(f"未使用的文件: {len(unused_files)}")

    if unused_files:
        print(f"\n未使用的文件列表 (可以删除):")
        total_size = 0
        for filename, reason in unused_files:
            file_path = texture_dir / filename
            if file_path.exists():
                size = file_path.stat().st_size
                total_size += size
                print(f"  - {filename} ({reason}) - {size} bytes")
            else:
                print(f"  - {filename} ({reason})")

        print(f"\n总共可以节省空间: {total_size} bytes ({total_size/1024:.1f} KB)")

    return used_files, unused_files

def create_delete_script(unused_files):
    """创建删除脚本"""
    texture_dir = Path("src/main/resources/textures")

    with open("delete_unused_textures.py", "w", encoding="utf-8") as f:
        f.write("#!/usr/bin/env python3\n")
        f.write("# -*- coding: utf-8 -*-\n")
        f.write('"""\n删除1.8.8版本中未使用的贴图文件\n"""\n\n')
        f.write("import os\nfrom pathlib import Path\n\n")
        f.write("def delete_unused_textures():\n")
        f.write('    """删除未使用的贴图文件"""\n')
        f.write(f'    texture_dir = Path("src/main/resources/textures")\n')
        f.write("    \n")
        f.write("    files_to_delete = [\n")

        for filename, reason in unused_files:
            f.write(f'        "{filename}",  # {reason}\n')

        f.write("    ]\n\n")
        f.write("    deleted_count = 0\n")
        f.write("    total_size = 0\n")
        f.write("    \n")
        f.write("    for filename in files_to_delete:\n")
        f.write("        file_path = texture_dir / filename\n")
        f.write("        if file_path.exists():\n")
        f.write("            size = file_path.stat().st_size\n")
        f.write("            total_size += size\n")
        f.write("            file_path.unlink()\n")
        f.write("            deleted_count += 1\n")
        f.write('            print(f"删除: {filename} ({size} bytes)")\n')
        f.write("        else:\n")
        f.write('            print(f"文件不存在: {filename}")\n')
        f.write("    \n")
        f.write('    print(f"\\n删除完成: {deleted_count} 个文件, 节省空间: {total_size} bytes ({total_size/1024:.1f} KB)")\n\n')
        f.write('if __name__ == "__main__":\n')
        f.write("    delete_unused_textures()\n")

    print(f"已创建删除脚本: delete_unused_textures.py")
    print(f"运行 'python delete_unused_textures.py' 来删除未使用的文件")

if __name__ == "__main__":
    used_files, unused_files = analyze_textures()

    if unused_files:
        print(f"\n是否创建删除脚本? (y/n): ", end="")
        try:
            response = input().lower().strip()
            if response in ['y', 'yes', '是', '确定']:
                create_delete_script(unused_files)
        except:
            # 如果无法获取输入，直接创建脚本
            create_delete_script(unused_files)
