#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析现有图片文件和代码中的映射关系
找出需要添加的映射和需要移除的映射
"""

import os
import re
from pathlib import Path

def get_existing_texture_files():
    """获取现有的贴图文件列表"""
    texture_dir = Path("src/main/resources/textures")
    
    if not texture_dir.exists():
        print(f"贴图目录不存在: {texture_dir}")
        return []
    
    # 获取所有图片文件
    image_files = []
    for ext in ['*.png', '*.jpg', '*.jpeg', '*.gif']:
        image_files.extend(texture_dir.glob(ext))
    
    # 返回文件名（不含扩展名）
    return [file.stem for file in image_files if file.stem not in ['README', 'enchanted_item_glint']]

def get_mapped_textures_from_code():
    """从代码中提取已映射的贴图"""
    texture_manager_file = Path("src/main/java/cn/winde/cuilian/texture/TextureManager.java")
    
    if not texture_manager_file.exists():
        print(f"TextureManager.java 不存在")
        return {}
    
    content = texture_manager_file.read_text(encoding='utf-8')
    
    # 提取 addTextureMapping 调用
    pattern = r'addTextureMapping\(Material\.(\w+),\s*"([^"]+)"\)'
    matches = re.findall(pattern, content)
    
    mapped_textures = {}
    for material, texture_name in matches:
        mapped_textures[texture_name] = material
    
    return mapped_textures

def get_1_8_8_materials():
    """获取1.8.8版本支持的Material列表"""
    return {
        # 装备类
        'LEATHER_HELMET', 'LEATHER_CHESTPLATE', 'LEATHER_LEGGINGS', 'LEATHER_BOOTS',
        'CHAINMAIL_HELMET', 'CHAINMAIL_CHESTPLATE', 'CHAINMAIL_LEGGINGS', 'CHAINMAIL_BOOTS',
        'IRON_HELMET', 'IRON_CHESTPLATE', 'IRON_LEGGINGS', 'IRON_BOOTS',
        'DIAMOND_HELMET', 'DIAMOND_CHESTPLATE', 'DIAMOND_LEGGINGS', 'DIAMOND_BOOTS',
        'GOLD_HELMET', 'GOLD_CHESTPLATE', 'GOLD_LEGGINGS', 'GOLD_BOOTS',
        
        # 武器类
        'WOOD_SWORD', 'STONE_SWORD', 'IRON_SWORD', 'DIAMOND_SWORD', 'GOLD_SWORD',
        'WOOD_AXE', 'STONE_AXE', 'IRON_AXE', 'DIAMOND_AXE', 'GOLD_AXE',
        'WOOD_PICKAXE', 'STONE_PICKAXE', 'IRON_PICKAXE', 'DIAMOND_PICKAXE', 'GOLD_PICKAXE',
        'WOOD_SPADE', 'STONE_SPADE', 'IRON_SPADE', 'DIAMOND_SPADE', 'GOLD_SPADE',
        'WOOD_HOE', 'STONE_HOE', 'IRON_HOE', 'DIAMOND_HOE', 'GOLD_HOE',
        'BOW', 'FISHING_ROD', 'FLINT_AND_STEEL', 'SHEARS',
        
        # 食物类
        'APPLE', 'BREAD', 'COOKED_BEEF', 'COOKED_CHICKEN', 'COOKED_FISH', 'GOLDEN_APPLE',
        'GOLDEN_CARROT', 'CAKE', 'COOKIE', 'MELON', 'CARROT_ITEM', 'POTATO_ITEM', 'BAKED_POTATO',
        'PORK', 'GRILLED_PORK', 'RAW_FISH', 'MUSHROOM_SOUP', 'ROTTEN_FLESH',
        'SPIDER_EYE', 'FERMENTED_SPIDER_EYE', 'MAGMA_CREAM', 'GLISTERING_MELON',
        
        # 材料类
        'DIAMOND', 'EMERALD', 'GOLD_INGOT', 'IRON_INGOT', 'COAL', 'REDSTONE', 'INK_SACK',
        'QUARTZ', 'STICK', 'STRING', 'FEATHER', 'LEATHER', 'PAPER', 'BOOK', 'SLIME_BALL',
        'ENDER_PEARL', 'BLAZE_ROD', 'GHAST_TEAR', 'NETHER_STAR', 'GOLD_NUGGET',
        'CLAY_BALL', 'CLAY_BRICK', 'NETHER_BRICK_ITEM', 'PRISMARINE_SHARD', 'PRISMARINE_CRYSTALS',
        
        # 工具和实用物品
        'BUCKET', 'WATER_BUCKET', 'LAVA_BUCKET', 'MILK_BUCKET', 'COMPASS', 'WATCH',
        'MAP', 'NAME_TAG', 'SADDLE', 'ITEM_FRAME', 'FLOWER_POT_ITEM', 'CARROT_STICK',
        'EMPTY_MAP', 'SKULL_ITEM',
        
        # 药水和附魔
        'POTION', 'GLASS_BOTTLE', 'ENCHANTED_BOOK', 'EXP_BOTTLE', 'BREWING_STAND_ITEM',
        
        # 箭矢和投掷物
        'ARROW', 'SNOW_BALL', 'EGG', 'FIREBALL',
        
        # 其他常见物品
        'BONE', 'SUGAR_CANE', 'FLINT', 'GLOWSTONE_DUST', 'BLAZE_POWDER', 'NETHER_STALK',
        'SULPHUR', 'SUGAR', 'WHEAT', 'WHEAT_SEEDS', 'MELON_SEEDS', 'PUMPKIN_SEEDS',
        'SEEDS', 'COCOA', 'PAINTING', 'SIGN', 'WOOD_DOOR', 'IRON_DOOR_BLOCK',
        'REDSTONE_COMPARATOR', 'DIODE', 'MINECART', 'STORAGE_MINECART', 'POWERED_MINECART',
        'EXPLOSIVE_MINECART', 'HOPPER_MINECART', 'COMMAND_MINECART', 'BOAT', 'RECORD_11',
        'RECORD_13', 'RECORD_BLOCKS', 'RECORD_CAT', 'RECORD_CHIRP', 'RECORD_FAR',
        'RECORD_MALL', 'RECORD_MELLOHI', 'RECORD_STAL', 'RECORD_STRAD', 'RECORD_WAIT', 'RECORD_WARD'
    }

def suggest_material_mapping(texture_name):
    """根据贴图文件名建议Material映射"""
    # 标准化名称
    name_upper = texture_name.upper().replace('-', '_')
    
    # 特殊映射规则
    special_mappings = {
        'COOKED_COD': 'COOKED_FISH',
        'COOKED_PORKCHOP': 'GRILLED_PORK',
        'BEEF': 'PORK',  # 1.8中可能没有单独的牛肉
        'CHICKEN': 'RAW_CHICKEN',
        'PORKCHOP': 'PORK',
        'MELON_SLICE': 'MELON',
        'CARROT': 'CARROT_ITEM',
        'POTATO': 'POTATO_ITEM',
        'LAPIS_LAZULI': 'INK_SACK',
        'COMPASS_00': 'COMPASS',
        'CLOCK_00': 'WATCH',
        'FILLED_MAP': 'MAP',
        'EXPERIENCE_BOTTLE': 'EXP_BOTTLE',
        'SNOWBALL': 'SNOW_BALL',
        'FIRE_CHARGE': 'FIREBALL',
        'GUNPOWDER': 'SULPHUR',
        'NETHER_WART': 'NETHER_STALK',
        'BRICK': 'CLAY_BRICK',
        'GOLDEN_HELMET': 'GOLD_HELMET',
        'GOLDEN_CHESTPLATE': 'GOLD_CHESTPLATE',
        'GOLDEN_LEGGINGS': 'GOLD_LEGGINGS',
        'GOLDEN_BOOTS': 'GOLD_BOOTS',
        'GOLDEN_SWORD': 'GOLD_SWORD',
        'GOLDEN_PICKAXE': 'GOLD_PICKAXE',
        'GOLDEN_AXE': 'GOLD_AXE',
        'GOLDEN_SHOVEL': 'GOLD_SPADE',
        'GOLDEN_HOE': 'GOLD_HOE',
        'WOODEN_SWORD': 'WOOD_SWORD',
        'WOODEN_PICKAXE': 'WOOD_PICKAXE',
        'WOODEN_AXE': 'WOOD_AXE',
        'WOODEN_SHOVEL': 'WOOD_SPADE',
        'WOODEN_HOE': 'WOOD_HOE',
        'DIAMOND_SHOVEL': 'DIAMOND_SPADE',
        'IRON_SHOVEL': 'IRON_SPADE',
        'STONE_SHOVEL': 'STONE_SPADE',
    }
    
    if name_upper in special_mappings:
        return special_mappings[name_upper]
    
    # 直接匹配
    return name_upper

def analyze_mappings():
    """分析映射关系"""
    print("分析贴图文件和代码映射关系...")
    print("=" * 60)
    
    # 获取数据
    existing_files = get_existing_texture_files()
    mapped_textures = get_mapped_textures_from_code()
    supported_materials = get_1_8_8_materials()
    
    print(f"现有贴图文件: {len(existing_files)} 个")
    print(f"代码中已映射: {len(mapped_textures)} 个")
    print(f"1.8.8支持的Material: {len(supported_materials)} 个")
    
    # 分析未映射的文件
    unmapped_files = []
    for file in existing_files:
        if file not in mapped_textures:
            # 跳过时钟和指南针的动画帧
            if file.startswith('clock_') or file.startswith('compass_'):
                continue
            unmapped_files.append(file)
    
    # 分析映射到不存在文件的情况
    missing_files = []
    for texture_name, material in mapped_textures.items():
        if texture_name not in existing_files:
            missing_files.append((texture_name, material))
    
    # 分析映射到不支持Material的情况
    unsupported_mappings = []
    for texture_name, material in mapped_textures.items():
        if material not in supported_materials:
            unsupported_mappings.append((texture_name, material))
    
    print("\n" + "=" * 60)
    print("分析结果:")
    
    if unmapped_files:
        print(f"\n未映射的贴图文件 ({len(unmapped_files)} 个):")
        for file in sorted(unmapped_files):
            suggested_material = suggest_material_mapping(file)
            if suggested_material in supported_materials:
                print(f"  + {file} -> 建议映射到 {suggested_material}")
            else:
                print(f"  - {file} -> 1.8.8不支持")
    
    if missing_files:
        print(f"\n映射到不存在文件的情况 ({len(missing_files)} 个):")
        for texture_name, material in sorted(missing_files):
            print(f"  - {texture_name} -> {material} (文件不存在)")
    
    if unsupported_mappings:
        print(f"\n映射到1.8.8不支持Material的情况 ({len(unsupported_mappings)} 个):")
        for texture_name, material in sorted(unsupported_mappings):
            print(f"  - {texture_name} -> {material} (1.8.8不支持)")
    
    # 生成建议的代码
    print(f"\n建议添加的映射代码:")
    print("// 添加缺失的1.8.8支持的贴图映射")
    for file in sorted(unmapped_files):
        suggested_material = suggest_material_mapping(file)
        if suggested_material in supported_materials:
            print(f'addTextureMapping(Material.{suggested_material}, "{file}");')
    
    return {
        'unmapped_files': unmapped_files,
        'missing_files': missing_files,
        'unsupported_mappings': unsupported_mappings
    }

if __name__ == "__main__":
    analyze_mappings()
