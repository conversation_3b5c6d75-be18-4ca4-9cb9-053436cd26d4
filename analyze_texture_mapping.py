#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析现有图片文件和代码映射的对比
"""

from pathlib import Path
import re

def get_existing_images():
    """获取现有的图片文件列表"""
    texture_dir = Path("src/main/resources/textures")
    if not texture_dir.exists():
        return []
    
    images = []
    for file in texture_dir.glob("*.png"):
        if file.name != "README.md":
            images.append(file.stem)  # 不包含扩展名
    
    return sorted(images)

def get_mapped_textures():
    """从TextureManager.java中提取已映射的贴图"""
    java_file = Path("src/main/java/cn/winde/cuilian/texture/TextureManager.java")
    if not java_file.exists():
        return []
    
    content = java_file.read_text(encoding='utf-8')
    
    # 提取addTextureMapping调用中的文件名
    pattern = r'addTextureMapping\([^,]+,\s*"([^"]+)"\)'
    matches = re.findall(pattern, content)
    
    return sorted(set(matches))

def analyze_mapping():
    """分析映射情况"""
    existing_images = get_existing_images()
    mapped_textures = get_mapped_textures()
    
    print("=== 贴图文件映射分析 ===")
    print(f"现有图片文件: {len(existing_images)} 个")
    print(f"代码中映射: {len(mapped_textures)} 个")
    print()
    
    # 找出存在但未映射的文件
    unmapped_images = set(existing_images) - set(mapped_textures)
    print(f"存在但未映射的文件 ({len(unmapped_images)} 个):")
    for img in sorted(unmapped_images):
        print(f"  - {img}.png")
    
    print()
    
    # 找出映射但不存在的文件
    missing_images = set(mapped_textures) - set(existing_images)
    print(f"映射但文件不存在 ({len(missing_images)} 个):")
    for img in sorted(missing_images):
        print(f"  - {img}.png")
    
    print()
    
    # 正确映射的文件
    correct_mappings = set(existing_images) & set(mapped_textures)
    print(f"正确映射的文件 ({len(correct_mappings)} 个):")
    for img in sorted(correct_mappings):
        print(f"  ✓ {img}.png")
    
    return unmapped_images, missing_images, correct_mappings

def generate_material_mappings(unmapped_images):
    """为未映射的图片生成Material映射建议"""
    print("\n=== 建议的Material映射 ===")
    
    # 1.8.8版本的Material映射规则
    material_mappings = {}
    
    for img in sorted(unmapped_images):
        # 转换为可能的Material名称
        material_name = img.upper().replace('-', '_')
        
        # 特殊映射规则
        special_mappings = {
            'COOKED_PORKCHOP': 'GRILLED_PORK',  # 1.8中的名称
            'BEEF': 'RAW_BEEF',
            'CHICKEN': 'RAW_CHICKEN', 
            'PORKCHOP': 'PORK',
            'CARROT_ON_A_STICK': 'CARROT_STICK',
            'MELON_SLICE': 'MELON',
            'WHEAT_SEEDS': 'SEEDS',
            'MELON_SEEDS': 'MELON_SEEDS',
            'PUMPKIN_SEEDS': 'PUMPKIN_SEEDS',
            'COCOA_BEANS': 'INK_SACK',  # 1.8中可可豆是INK_SACK的数据值
            'BONE_MEAL': 'INK_SACK',    # 1.8中骨粉是INK_SACK的数据值
            'INK_SAC': 'INK_SACK',
            'COMPARATOR': 'REDSTONE_COMPARATOR',
            'BREWING_STAND': 'BREWING_STAND_ITEM',
            'CAULDRON': 'CAULDRON_ITEM',
            'FLOWER_POT': 'FLOWER_POT_ITEM',
            'HOPPER': 'HOPPER_ITEM',
            'IRON_DOOR': 'IRON_DOOR_ITEM',
            'OAK_BOAT': 'BOAT',
            'OAK_DOOR': 'WOOD_DOOR',
            'OAK_SIGN': 'SIGN',
            'CHEST_MINECART': 'STORAGE_MINECART',
            'FURNACE_MINECART': 'POWERED_MINECART',
            'TNT_MINECART': 'EXPLOSIVE_MINECART',
            'POISONOUS_POTATO': 'POISONOUS_POTATO',  # 1.8中存在
            'NETHER_BRICK': 'NETHER_BRICK_ITEM',
            'NETHER_WART': 'NETHER_STALK',
            'SUGAR_CANE': 'SUGAR_CANE_ITEM',
            'ROTTEN_FLESH': 'ROTTEN_FLESH',
            'SPIDER_EYE': 'SPIDER_EYE',
            'MAGMA_CREAM': 'MAGMA_CREAM',
            'GOLD_NUGGET': 'GOLD_NUGGET',
            'GUNPOWDER': 'SULPHUR',
            'CHARCOAL': 'COAL',  # 1.8中木炭和煤炭是同一个Material的不同数据值
            'PUMPKIN_PIE': 'PUMPKIN_PIE',
            'MUSHROOM_STEW': 'MUSHROOM_SOUP',
            'ENDER_EYE': 'EYE_OF_ENDER',
            'PAINTING': 'PAINTING',
            'ITEM_FRAME': 'ITEM_FRAME',
        }
        
        # 音乐唱片映射
        if img.startswith('MUSIC_DISC_'):
            disc_name = img.replace('MUSIC_DISC_', 'RECORD_')
            material_mappings[img] = disc_name
        elif material_name in special_mappings:
            material_mappings[img] = special_mappings[material_name]
        else:
            material_mappings[img] = material_name
    
    # 输出映射建议
    for img, material in material_mappings.items():
        print(f"addTextureMapping(Material.{material}, \"{img}\");")
    
    return material_mappings

if __name__ == "__main__":
    unmapped, missing, correct = analyze_mapping()
    
    if unmapped:
        print("\n" + "="*50)
        generate_material_mappings(unmapped)
    
    print(f"\n总结:")
    print(f"- 需要添加映射: {len(unmapped)} 个")
    print(f"- 需要移除映射: {len(missing)} 个") 
    print(f"- 正确映射: {len(correct)} 个")
